<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <Agents />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import Agents from '~/features/Agents/index.vue'
import { MIDDLEWARES } from '~/constants/middlewares'

const app = useApp()

definePageMeta({
  middleware: [MIDDLEWARES.AUTH_ADMIN],
})

app.updateDocMeta({ title: routes.agents.name })
app.updatePageMeta({
  title: routes.agents.name,
  breadcrumbs: [routes.agents],
})
</script>
