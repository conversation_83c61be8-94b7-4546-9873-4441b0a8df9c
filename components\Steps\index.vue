<template>
  <div class="steps">
    <nav :class="['steps-header', size, stepHeaderClass]">
      <div v-for="(step, index) in options.steps" :key="`step-${index}`" class="step-item-wrapper">
        <div :class="['step-item', stepItemClass]">
          <div :class="['step-item-line', stepStatus(index), index === 0 ? 'opacity-0' : '']" />
          <div
            :class="['step-item-body', clickAble, stepStatus(index)]"
            @click="handleChangeStep(index)"
          >
            <Transition
              enter-active-class="transition transition-opacity duration-100"
              leave-active-class="transition transition-opacity duration-100"
              enter-from-class="opacity-0"
              leave-to-class="opacity-0"
            >
              <em v-if="stepStatus(index) === 'completed'" :class="['body-icon', icon]" />
            </Transition>
            <p :class="['body-label', stepItemLabelClass]">
              {{ step.label }}
            </p>
          </div>
          <div
            :class="[
              'step-item-line',
              stepStatus(index + 1),
              index === options.steps.length - 1 ? 'opacity-0' : '',
            ]"
          />
        </div>
      </div>
    </nav>
    <div class="steps-body">
      <div v-for="(option, indexKey) in options.steps" :key="`content-${indexKey}`">
        <div v-if="currentStepIndex === indexKey" :class="['step-panel', stepPanelClass]">
          <slot :change-tab="handleChangeStep" :index="indexKey" :option="option" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { IStepOptions } from './types'

const props = withDefaults(
  defineProps<{
    value?: number
    options: IStepOptions
    stepSize?: 'sm' | 'md' | 'lg' | 'xl'
    stepHeaderClass?: string | string[] | object
    stepItemClass?: string | string[] | object
    stepItemLabelClass?: string | string[] | object
    stepPanelClass?: string | string[] | object
  }>(),
  {
    value: 0,
    stepSize: 'md',
  }
)

const emit = defineEmits<{
  (event: 'update:modelValue', value: number): void
}>()

const router = useRouter()

const currentStepIndex = ref<number>(props.value)

const size = computed(() => {
  switch (props.stepSize) {
    case 'sm':
      return 'max-w-screen-sm mx-auto'
    case 'md':
      return 'max-w-screen-md mx-auto'
    case 'lg':
      return 'max-w-screen-lg mx-auto'
    case 'xl':
      return 'max-w-screen-xl mx-auto'
    default:
      return 'w-full'
  }
})

const icon = computed(() => `ic ic-${props.options.stepCompletedIcon ?? 'check-solid'}`)
const clickAble = computed(() => (props.options.isStepClickable ? 'cursor-pointer' : ''))

watch(
  () => props.value,
  (value) => {
    if (value <= props.options.steps.length - 1 && value >= 0) {
      currentStepIndex.value = value

      if (!props.options.isNotChangeRoute) {
        router.push({ query: { step: props.options.steps[value].value } })
      }
    }
  }
)

watch(
  () => currentStepIndex.value,
  (value) => {
    emit('update:modelValue', value)
  }
)

const stepStatus = (index: number) => {
  if (index < currentStepIndex.value) {
    return 'completed'
  } else if (index === currentStepIndex.value) {
    return 'current'
  }

  return 'pending'
}

const handleChangeStep = (step: number) => {
  if (!props.options.isStepClickable) return

  if (step <= props.options.steps.length - 1 && step >= 0) {
    currentStepIndex.value = step
  }
}
</script>
