<template>
  <div class="grid grid-cols-2 gap-4">
    <Modal v-model="isShowRegisterModal" title="ลงทะเบียน SCB Easy App" class="modal-md">
      <AutoSCBEasyAppForm
        :request-status="otpRequest.status.value"
        :submit-status="otpSubmit.status.value"
        @otp-submit="otpRequest.run"
        @submit="otpSubmit.run"
      />
    </Modal>
    <div>
      <div class="text-lg flex items-center">
        <p class="font-bold">SCB Easy App</p>
        <div class="ml-4">
          <span v-if="item.scb_bank_device" class="text-danger text-sm">
            <i class="ic ic-check-circle-solid" /> ลงทะเบียนแล้ว
          </span>
          <span v-else class="text-danger text-sm">
            <i class="ic ic-x-circle-solid" /> ยังไม่ลงทะเบียน
          </span>
        </div>
      </div>
      <p class="text-sm text-gray">(รองรับฝากและถอน)</p>
    </div>
    <div>
      <Button class="btn-success btn-sm" @click="isShowRegisterModal = true">
        {{ item.scb_bank_device ? 'ลงทะเบียนใหม่' : 'ลงทะเบียน' }}
      </Button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue'
import { IBankItem } from '~/models/bank'
import AutoSCBEasyAppForm from '~/features/agent/AgentBankSingle/AutoSCBEasyAppForm.vue'
import {
  useAgentBankPageLoader,
  useAgentBankSCBRequestOTPLoader,
  useAgentBankSCBSubmitOTPLoader,
} from '~/loaders/agentBank'

const props = defineProps({
  item: {
    type: Object as PropType<IBankItem>,
    required: true,
  },
})

const otpRequest = useAgentBankSCBRequestOTPLoader(props.item.id)
const otpSubmit = useAgentBankSCBSubmitOTPLoader(props.item.id)
const dialog = useDialog()
const bank = useAgentBankPageLoader()
const isShowRegisterModal = ref(false)

useWatchTrue(
  () => otpRequest.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: otpRequest.status.value.errorData,
    })
  }
)

useWatchTrue(
  () => otpSubmit.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: otpRequest.status.value.errorData,
    })
  }
)

useWatchTrue(
  () => otpSubmit.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ลงทะเบียน SCB Easy App สำเร็จ',
      })
      .finally(() => {
        isShowRegisterModal.value = false
        bank.find(props.item.id)
      })
  }
)
</script>
