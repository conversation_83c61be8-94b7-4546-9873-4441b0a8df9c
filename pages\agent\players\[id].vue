<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentPlayerSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta, useAgentPlayerPageLoader } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentPlayerSingle from '~/features/agent/AgentPlayerSingle/index.vue'

const player = useAgentPlayerPageLoader()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_AGENT,
    async (to) => {
      const player = useAgentPlayerPageLoader()

      await player.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(player.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const route = useRoute()
const app = useApp()

const r = routes.agentPLayerEdit(
  route.params.id as string,
  player.findItem?.first_name + ' ' + player.findItem?.last_name
)

app.updateDocMeta({ title: r.name })
app.updatePageMeta({
  title: r.name,
  breadcrumbs: [routes.agentPlayers, r],
})
</script>
