<template>
  <div>
    <Modal v-model="isShowChangePassModal" title="เปลี่ยนรหัสผ่าน" class="modal-md">
      <ChangePassForm :status="changePass.status.value" @submit="changePass.run" />
    </Modal>
    <Modal v-model="isShowUpdateBankModal" title="แก้ไขข้อมูลธนาคาร" class="modal-md">
      <UpdateBankForm :status="updateBank.status.value" @submit="updateBank.run" />
    </Modal>
    <Modal v-model="isShowAddPromotionModal" title="แอดโปรโมชั่น" class="modal-md">
      <AddPromotionForm :status="addPromotion.status.value" @submit="addPromotion.run" />
    </Modal>
    <Modal
      v-model="isShowAddCreditModal"
      class="modal-md"
      :no-backdrop-close="true"
      title="เพิ่มเครดิต"
    >
      <CreditForm :status="creditPlus.status.value" @submit="creditPlus.run" />
    </Modal>
    <Modal
      v-model="isShowRemoveCreditModal"
      class="modal-md"
      :no-backdrop-close="true"
      title="ลบเครดิต"
    >
      <CreditForm :status="creditMinus.status.value" @submit="creditMinus.run" />
    </Modal>
    <Menu as="div" class="relative inline-block text-left">
      <div>
        <MenuButton as="div">
          <Button :is-only-icon="true" class="btn-info" icon="gear-solid" />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none z-10"
        >
          <div class="px-1 py-1">
            <nuxt-link
              :href="routes.agentPLayerEdit(item.props.item.id).to"
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white',
              ]"
            >
              ดูรายละเอียด
            </nuxt-link>
            <p
              v-for="menuItem in menuItems"
              :key="menuItem.title"
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="menuItem.onClick"
            >
              {{ menuItem.title }}
            </p>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>
<script lang="tsx" setup>
import { Menu, MenuButton, MenuItems } from '@headlessui/vue'
import { PropType } from 'vue'
import { IRowItem } from '~/components/Table/types'
import { IPlayerItem } from '~/models/player'
import AddPromotionForm from '~/features/agent/AgentPlayerSingle/AddPromotionForm.vue'
import UpdateBankForm from '~/features/agent/AgentPlayerSingle/UpdateBankForm.vue'
import ChangePassForm from '~/features/agent/AgentPlayerSingle/ChangePassForm.vue'
import {
  useAgentPlayerChangePasswordLoader,
  useAgentPlayerCreditMinusLoader,
  useAgentPlayerCreditPlusLoader,
  useAgentPlayerUpdateBankLoader,
} from '~/loaders/player'
import CreditForm from '~/features/agent/AgentPlayerSingle/CreditForm.vue'
import {
  useAgentPromotionRedeemHistoryAddLoader,
  useAgentPromotionRedeemHistoryDeleteLoader,
} from '~/loaders/agentPromotion'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ item: IPlayerItem }>>,
    required: true,
  },
})

const changePass = useAgentPlayerChangePasswordLoader(props.item.props!.item.id)
const updateBank = useAgentPlayerUpdateBankLoader(props.item.props!.item.id)
const creditPlus = useAgentPlayerCreditPlusLoader(props.item.props!.item.id)
const creditMinus = useAgentPlayerCreditMinusLoader(props.item.props!.item.id)
const kickPromotion = useAgentPromotionRedeemHistoryDeleteLoader(props.item.props!.item.id)
const addPromotion = useAgentPromotionRedeemHistoryAddLoader(props.item.props!.item.id)
const isShowChangePassModal = ref(false)
const isShowUpdateBankModal = ref(false)
const isShowAddCreditModal = ref(false)
const isShowRemoveCreditModal = ref(false)
const isShowAddPromotionModal = ref(false)
const dialog = useDialog()

const menuItems = [
  {
    title: 'แอดโปรโมชั่น',
    onClick: () => {
      isShowAddPromotionModal.value = true
    },
  },
  {
    title: 'เตะโปรโมชั่น',
    onClick: () => {
      dialog
        .warning({
          title: 'ยืนยัน',
          message: 'คุณต้องการเตะโปรโมชั่นหรือไม่?',
          isShowCancelBtn: true,
        })
        .then(() => {
          kickPromotion.run()
        })
    },
  },
  {
    title: 'เปลี่ยนรหัสผ่าน',
    onClick: () => {
      isShowChangePassModal.value = true
    },
  },
  {
    title: 'เพิ่มเครดิต',
    onClick: () => {
      isShowAddCreditModal.value = true
    },
  },
  {
    title: 'ยึดเครดิต',
    onClick: () => {
      isShowRemoveCreditModal.value = true
    },
  },
  {
    title: 'แก้ไขข้อมูลธนาคาร',
    onClick: () => {
      isShowUpdateBankModal.value = true
    },
  },
]

useWatchTrue(
  () => changePass.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'เปลี่ยนรหัสผ่านสำเร็จ',
      })
      .then(() => {
        isShowChangePassModal.value = false
      })
  }
)

useWatchTrue(
  () => changePass.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(changePass.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => updateBank.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขข้อมูลธนาคารสำเร็จ',
      })
      .then(() => {
        isShowUpdateBankModal.value = false
      })
  }
)

useWatchTrue(
  () => updateBank.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(updateBank.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => creditPlus.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'เพิ่มเครดิตสำเร็จ',
      })
      .then(() => {
        isShowAddCreditModal.value = false
      })
  }
)

useWatchTrue(
  () => creditPlus.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(creditPlus.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => creditMinus.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ลบเครดิตสำเร็จ',
      })
      .then(() => {
        isShowRemoveCreditModal.value = false
      })
  }
)

useWatchTrue(
  () => creditMinus.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(creditMinus.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => kickPromotion.status.value.isSuccess,
  () => {
    dialog.success({
      title: 'สำเร็จ',
      message: 'เตะโปรโมชั่นสำเร็จ',
    })
  }
)

useWatchTrue(
  () => kickPromotion.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(kickPromotion.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => addPromotion.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แอดโปรโมชั่นสำเร็จ',
      })
      .then(() => {
        isShowAddPromotionModal.value = false
      })
  }
)

useWatchTrue(
  () => addPromotion.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(addPromotion.status.value.errorData),
    })
  }
)
</script>
