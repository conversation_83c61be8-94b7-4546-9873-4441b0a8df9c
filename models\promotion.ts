import { type IAgentItem } from './agent'
import {
  type BonusType,
  type DueDateType,
  type PromotionSubType,
  type PromotionType,
  type RedeemByType,
  type UserTypeReceiver,
  type WithdrawStatus,
} from '~/constants/promotion'
import { type IPlayerItem } from '~/models/player'

export interface IPromotionConditionItem {
  id: string
  promotion_id: string
  min_deposit_amount: number
  max_deposit_amount: number
  percent_payout: number
  bonus_payout_amount: number
  max_withdraw_amount: number
  created_at: string
  updated_at: string
}

export interface IPromotionItem {
  id: string
  name_th: string
  name_en: string
  topic_th: string
  topic_en: string
  description_th: string
  description_en: string
  promotion_type: PromotionType
  promotion_sub_type: PromotionSubType
  bonus_type: BonusType
  user_type_receiver: UserTypeReceiver
  due_date_type: DueDateType
  start_date: string
  end_date: string
  redeem_by_type: RedeemByType
  auto_withdraw_status: WithdrawStatus
  idle_deposit_day: number
  slot_turnover: number
  baccarat_turnover: number
  min_deposit_amount: number
  max_deposit_amount: number
  max_bonus_payout_amount: number
  min_deposit_point: number
  point_to_player: number

  provider_ids: string[]
  image_url_th: string
  image_url_en: string
  is_active: boolean
  is_display: boolean
  conditions: IPromotionConditionItem[]

  agent_id: string
  agent: IAgentItem

  sort_order: number
  created_at: string
  updated_at: string
}

export interface IPromotionRedeemItem {
  id: string
  created_at: string
  updated_at: string
  amount: number
  bonus_amount: number
  total_amount: number
  slot_turnover_amount: number
  baccarat_turnover_amount: number
  max_withdrawable_amount: number
  status: string
  point: number
  player_id: string
  player: IPlayerItem
  agent_id: string
  agent: IAgentItem
  promotion_id: string
  promotion: IPromotionItem
  deposit_id: string
  player_deposit: any
  withdraw_id: string
  player_withdraw: any

  slot_current_turnover: number
  baccarat_current_turnover: number
  slot_remaining_turnover: number
  baccarat_remaining_turnover: number
}
