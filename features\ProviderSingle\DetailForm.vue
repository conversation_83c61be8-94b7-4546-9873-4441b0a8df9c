<template>
  <Form @submit="onSubmit">
    <FormFields class="grid grid-cols-2 gap-4" :options="formFields" />
    <Button type="submit" class="btn-primary btn-min" :is-loading="status.isLoading">
      บันทึก
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IProviderItem } from '~/models/provider'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item: IProviderItem
}>()

const form = useForm<Partial<IProviderItem>>({
  initialValues: props.item,
})

watch(
  () => props.item,
  (item) => {
    form.setValues(item)
  }
)

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'name',
      isDisabled: true,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อย่อ',
      name: 'short_name',
      isDisabled: true,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TAG,
    props: {
      label: 'ประเภท',
      name: 'type',
      rules: yup.array().min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เปอร์เซ็นต์',
      name: 'percent',
      rules: yup.number().integer().max(100).min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ลำดับ',
      name: 'no',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'สถานะ',
      name: 'status',
      options: [
        {
          label: 'เปิดใช้งาน',
          value: 'active',
        },
        {
          label: 'ปรับปรุง',
          value: 'pending',
        },
        {
          label: 'ปิดใช้งาน',
          value: 'inactive',
        },
      ],
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'รูปภาพ',
      name: 'image_url',
      rules: yup.string().url().required(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    no: Number(data.no),
    percent: Number(data.percent),
  })
})
</script>
