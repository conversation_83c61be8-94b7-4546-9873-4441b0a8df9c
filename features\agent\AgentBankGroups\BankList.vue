<template>
  <div class="p-2 space-y-2">
    <p v-if="isEmpty()">ไม่มีบัญชี</p>
    <div
      v-for="bank in items"
      v-else
      :key="bank.id"
      class="flex whitespace-nowrap items-center border p-2 rounded-md"
    >
      <Image
        :src="bank.bank.image_url"
        class="max-h-[50px] max-w-[50px] object-cover rounded mr-2"
      />
      <div>
        <p class="text-xs text-md text-gray">{{ bank.bank.name_th.replace('ธนาคาร', '') }}</p>
        <p class="font-bold text-md">{{ bank.account_name }}</p>
        <p class="text-xs">{{ bank.account_number }}</p>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { PropType } from 'vue'
import { IBankItem } from '~/models/bank'

const props = defineProps({
  items: {
    type: Array as PropType<IBankItem[]>,
    required: true,
  },
})

const isEmpty = (): boolean => {
  if (!ArrayHelper.isEmpty(props.items)) {
    if (!props.items?.at(0)) {
      return true
    }
  }

  return ArrayHelper.isEmpty(props.items)
}
</script>
