<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="สไตล์ไกด์" />
    <div class="text-description mb-6">
      กรุณาอ่าน Style Guide ก่อนเริ่มใช้งาน และใช้ความระมัดระวังในการเขียนโค้ดร่วมกันกับผู้อื่น
      ขอบคุณค่ะ
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Color Name</h1>
      <div class="grid grid-cols-4 gap-4">
        <div
          v-for="(color, index) in colors"
          :key="index"
          class="flex flex-col outline outline-gray-border"
        >
          <div :class="['w-full p-8', color.class]" />
          <h6 class="px-2 pt-2">
            {{ color.name }}
          </h6>
          <span class="px-2 pb-2 text-description text-sm">{{ color.description }}</span>
        </div>
      </div>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Typography</h1>
      <div class="bg-gray-fill p-2 rounded-10 my-2">
        <h3 class="display-heading">display-heading</h3>
        <span class="pb-2 text-description text-sm">
          สำหรับคำ ENG ล้วน ส่วนที่เป็นหัวข้อหลัก (เช่นตรง NavBar)
        </span>
      </div>
      <div class="bg-gray-fill p-2 rounded-10 my-2">
        <h3 class="page-heading">page-heading เพจเฮดดิ้ง</h3>
        <span class="pb-2 text-description text-sm">สำหรับ Title ชื่อ Page / หัวข้อหลัก</span>
      </div>
      <div class="bg-gray-fill p-2 rounded-10 my-2">
        <h3 class="topic-heading">topic-heading หัวข้อเฮดดิ้ง</h3>
        <span class="pb-2 text-description text-sm">สำหรับเน้นหัวข้อย่อย เช่น ใน Modal</span>
      </div>
      <div class="bg-gray-fill p-2 rounded-10 my-2">
        <h3 class="text-description">text-description คำอธิบาย</h3>
        <span class="pb-2 text-description text-sm">
          สำหรับส่วนคำอธิบายเพิ่มเติม ที่เป็นตัวสีเทา (ขนาดหลัก 18px)
        </span>
      </div>

      <h1 class="display-heading mt-8 mb-2">Typography Sizing</h1>
      <div class="bg-gray-fill p-2 rounded-10 my-2">
        <h3 class="text-3xl">
          text-3xl <span class="text-description text-sm">(ขนาดเทียบประมาณ 30px)</span>
        </h3>
        <h3 class="text-2xl">
          text-2xl <span class="text-description text-sm">(ขนาดเทียบประมาณ 24px)</span>
        </h3>
        <h3 class="text-xl">
          text-xl <span class="text-description text-sm">(ขนาดเทียบประมาณ 20px)</span>
        </h3>
        <h3 class="text-lg">
          text-lg <span class="text-description text-sm">(ขนาดเทียบประมาณ 18px)</span>
        </h3>
        <h3 class="text-base">
          text-base <span class="text-description text-sm">(ขนาดเทียบประมาณ 16px)</span>
        </h3>
        <h3 class="text-sm">
          text-sm <span class="text-description text-sm">(ขนาดเทียบประมาณ 14px)</span>
        </h3>
      </div>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { ref } from '#imports'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.styleguide.name })
app.updatePageMeta({
  title: styleguideMenu.styleguide.name,
  breadcrumbs: [styleguideMenu.styleguide],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))

const colors = ref<Array<{ name: string; class: string; description: string }>>([
  { name: 'primary', class: 'bg-primary', description: 'สีหลัก' },
  { name: 'primary-200', class: 'bg-primary-200', description: 'สีหลัก (ไฮไลท์)' },
  { name: 'secondary', class: 'bg-secondary', description: 'สีรอง' },
  { name: 'secondary-50', class: 'bg-secondary-50', description: 'สีรอง (ไฮไลท์)' },
  { name: 'primary-100', class: 'bg-primary-100', description: 'สีหลักพื้นหลังไล่สี(1)' },
  { name: 'primary-50', class: 'bg-primary-50', description: 'สีหลักพื้นหลังไล่สี(2)' },
  { name: 'dark', class: 'bg-dark', description: 'สีดำตัวหนังสือ' },
  { name: 'light', class: 'bg-light', description: 'สีขาวตัวหนังสือ/พื้นหลัง/พื้นหัวตาราง' },
  {
    name: 'gray',
    class: 'bg-gray',
    description: 'สีเทาตัวหนังสือ/อักษรหัวตาราง/อักษรเมนู/Field Border+Label',
  },
  {
    name: 'gray-disabled',
    class: 'bg-gray-disabled',
    description: 'Disabled Field Border+Label+Placeholder',
  },
  { name: 'gray-border', class: 'bg-gray-border', description: 'Table Border/Placeholder Text' },
  {
    name: 'gray-fill',
    class: 'bg-gray-fill',
    description: 'Disabled Field BG/Plain button hover:BG',
  },
  { name: 'success', class: 'bg-success', description: '' },
  { name: 'success-50', class: 'bg-success-50', description: '' },
  { name: 'danger', class: 'bg-danger', description: '' },
  { name: 'danger-50', class: 'bg-danger-50', description: '' },
  { name: 'info', class: 'bg-info', description: '' },
  { name: 'info-50', class: 'bg-info-50', description: '' },
  { name: 'warning', class: 'bg-warning', description: '' },
  { name: 'warning-50', class: 'bg-warning-50', description: '' },
])
</script>
