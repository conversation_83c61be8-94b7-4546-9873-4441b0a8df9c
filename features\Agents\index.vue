<template>
  <div>
    <PageHeader :title="routes.agents.name" />
    <div class="flex justify-end">
      <Modal
        v-model="isShowCreateModal"
        class="modal-md"
        title="สร้าง Agent"
        :no-backdrop-close="true"
      >
        <CreateForm :status="agent.addStatus.value" @submit="agent.add" />
      </Modal>
      <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">สร้าง</Button>
    </div>
    <Table :options="tableOptions" @pageChange="agent.fetch" @search="agent.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useAgentPageLoader } from '~/loaders/agent'
import { useTable } from '~/hooks/useTable'
import { IAgentItem } from '~/models/agent'
import { COLUMN_TYPES } from '~/components/Table/types'
import CreateForm from '~/features/Agents/CreateForm.vue'
import ColumnAction from '~/features/Agents/ColumnAction.vue'

const agent = useAgentPageLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)

agent.setFetchLoading()
onMounted(() => {
  agent.fetch()
})

useWatchTrue(
  () => agent.addStatus.value.isSuccess,
  () => {
    isShowCreateModal.value = false

    const userDefault = (agent.addItem.value as any).user as {
      username: string
      password_no_hash: string
    }

    dialog
      .success({
        title: 'สำเร็จ',
        message: `สร้าง Agent สำเร็จ \n ชื่อผู้ใช้: ${userDefault.username} \n รหัสผ่าน: ${userDefault.password_no_hash}`,
      })
      .then(() => {
        agent.fetch()
      })
  }
)

useWatchTrue(
  () => agent.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(agent.updateStatus.value.errorData),
    })
  }
)

const tableOptions = useTable<IAgentItem>({
  repo: agent,
  columns: () => [
    {
      value: 'ชื่อ',
    },
    {
      value: 'Prefix',
    },
    {
      value: 'โดเมน',
    },
    {
      value: 'เครดิต',
      className: 'text-right',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'หมายเหตุ',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.name,
          type: COLUMN_TYPES.LINK,
          props: {
            href: routes.agentEdit(item.id).to,
          },
        },
        {
          value: item.prefix,
          type: COLUMN_TYPES.LINK,
          props: {
            href: routes.agentEdit(item.id).to,
          },
        },
        {
          value: item.domain,
          type: COLUMN_TYPES.LINK,
          props: {
            href: item.domain,
            isNewTab: true,
          },
        },
        {
          value: item.credit,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.is_enabled,
          type: COLUMN_TYPES.BOOLEAN,
        },
        {
          value: item.remark,
          props: {
            max: 50,
          },
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            agent: item,
          },
        },
      ]
    }),
})
</script>
