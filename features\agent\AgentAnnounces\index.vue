<template>
  <div>
    <div>
      <PageHeader :title="routes.agentAnnounce.name" />
      <div class="flex justify-end">
        <Modal
          v-model="isShowCreateModal"
          class="modal-lg"
          title="สร้างประกาศ"
          :no-backdrop-close="true"
        >
          <CreateForm :status="announce.addStatus.value" @submit="announce.add" />
        </Modal>
        <Modal v-model="isShowEditModal" class="modal-lg" title="แก้ไข" :no-backdrop-close="true">
          <CreateForm
            :status="announce.updateStatus.value"
            :item="isActiveItem"
            @submit="onUpdateSubmit"
          />
        </Modal>
        <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">สร้าง</Button>
      </div>
      <Table :options="tableOptions" @pageChange="announce.fetch" @search="announce.search" />
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import CreateForm from '~/features/agent/AgentAnnounces/CreateForm.vue'
import { useAgentAnnouncePageLoader } from '~/loaders/agentAnnounce'
import { IAnnounceItem } from '~/models/article'

const announce = useAgentAnnouncePageLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)
const isShowEditModal = ref(false)
const isActiveItem = ref<IAnnounceItem | undefined>(undefined)

announce.setFetchLoading()
onMounted(() => {
  announce.fetch()
})

const onUpdateSubmit = (data: IAnnounceItem) => {
  announce.update(data.id, data)
}

const tableOptions = useTable<IAnnounceItem>({
  repo: announce,
  columns: () => [
    {
      value: 'หัวข้อ',
    },
    {
      value: 'เวลาที่แสดง',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'สร้างโดย',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.title,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          type: COLUMN_TYPES.BOOLEAN,
          value: item.is_enabled,
        },
        {
          value: item.created_by?.full_name,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          type: COLUMN_TYPES.ACTION,
          value: '',
          props: {
            isShowEdit: true,
          },
          on: {
            edit: () => {
              isActiveItem.value = item
              isShowEditModal.value = true
            },
            delete: () => {
              announce.remove(item.id)
            },
          },
        },
      ]
    }),
})

useWatchTrue(
  () => announce.addStatus.value.isSuccess,
  () => {
    isShowCreateModal.value = false

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างประกาศสำเร็จ',
      })
      .then(() => {
        announce.fetch()
      })
  }
)

useWatchTrue(
  () => announce.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(announce.updateStatus.value.errorData),
    })
  }
)

useWatchTrue(
  () => announce.updateStatus.value.isSuccess,
  () => {
    isShowEditModal.value = false
    isActiveItem.value = undefined

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขประกาศสำเร็จ',
      })
      .then(() => {
        announce.fetch()
      })
  }
)

useWatchTrue(
  () => announce.updateStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(announce.updateStatus.value.errorData),
    })
  }
)

useWatchTrue(
  () => announce.deleteStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ลบประกาศสำเร็จ',
      })
      .then(() => {
        announce.fetch()
      })
  }
)

useWatchTrue(
  () => announce.deleteStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(announce.deleteStatus.value.errorData),
    })
  }
)
</script>
