<template>
  <div>
    <Modal
      v-model="isShowEditModal"
      :no-backdrop-close="true"
      class="modal-md"
      :title="`แก้ไข ${item.props?.user.full_name}`"
    >
      <UserEditForm
        :status="user.updateStatus.value"
        :item="item.props?.user"
        @submit="onUpdateSubmit"
      />
    </Modal>
    <Modal
      v-model="isShowChangePasswordModal"
      :no-backdrop-close="true"
      class="modal-md"
      :title="`เปลี่ยนรหัสผ่าน ${item.props?.user.full_name}`"
    >
      <UserChangePasswordForm :status="changePassword.status.value" @submit="changePassword.run" />
    </Modal>
    <Menu as="div" class="relative inline-block text-left">
      <div>
        <MenuButton as="div">
          <Button :is-only-icon="true" class="btn-info" icon="gear-solid" />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute right-0 mt-2 z-20 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        >
          <div class="px-1 py-1">
            <p
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="isShowEditModal = true"
            >
              แก้ไข
            </p>
            <p
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="isShowChangePasswordModal = true"
            >
              เปลี่ยนรหัสผ่าน
            </p>
            <p
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="onToggleEnabled"
            >
              {{ item.props?.user.is_enabled ? 'ปิดการใช้งาน' : 'เปิดการใช้งาน' }}
            </p>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>
<script lang="tsx" setup>
import { Menu, MenuButton, MenuItems } from '@headlessui/vue'
import { PropType } from 'vue'
import { IRowItem } from '~/components/Table/types'
import { IUserItem } from '~/models/user'
import UserEditForm from '~/features/AgentSingle/UserEditForm.vue'
import { useAgentUserPageLoader } from '#imports'
import UserChangePasswordForm from '~/features/AgentSingle/UserChangePasswordForm.vue'
import { useUserChangePasswordLoader } from '~/loaders/user'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ user: IUserItem }, { reload: () => void }>>,
    required: true,
  },
})

const route = useRoute()
const dialog = useDialog()
const user = useAgentUserPageLoader(route.params.id as string)
const changePassword = useUserChangePasswordLoader(props.item.props?.user.id as string)
const isShowEditModal = ref(false)
const isShowChangePasswordModal = ref(false)
const enabled = useUserEnabledLoader(props.item.props?.user.id as string)

const onToggleEnabled = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: `ต้องการ${props.item.props?.user.is_enabled ? 'ปิดการใช้งาน' : 'เปิดการใช้งาน'} ${
        props.item.props?.user.full_name
      } ใช่หรือไม่`,
      cancelText: 'ยกเลิก',
      confirmText: 'ตกลง',
      isShowCancelBtn: true,
    })
    .then(() => {
      enabled.run({
        is_enabled: !props.item.props?.user.is_enabled,
      })
    })
}

useWatchTrue(
  () => enabled.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: props.item.props?.user.is_enabled ? 'เปิดการใช้งานสำเร็จ' : 'ปิดการใช้งานสำเร็จ',
      })
      .then(() => {
        props.item?.on?.reload()
      })
  }
)

useWatchTrue(
  () => enabled.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(enabled.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => user.updateStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขข้อมูลสำเร็จ',
      })
      .then(() => {
        isShowEditModal.value = false
        props.item?.on?.reload()
      })
  }
)

useWatchTrue(
  () => user.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(user.updateStatus.value.errorData),
    })
  }
)

useWatchTrue(
  () => changePassword.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'เปลี่ยนรหัสผ่านสำเร็จ',
      })
      .then(() => {
        isShowChangePasswordModal.value = false
      })
  }
)

useWatchTrue(
  () => changePassword.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(changePassword.status.value.errorData),
    })
  }
)

const onUpdateSubmit = (data: any) => {
  user.update(props.item?.props?.user.id as string, data)
}
</script>
