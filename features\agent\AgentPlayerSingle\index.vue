<template>
  <div>
    <Modal v-model="isShowChangePassModal" title="เปลี่ยนรหัสผ่าน" class="modal-md">
      <ChangePassForm :status="changePass.status.value" @submit="changePass.run" />
    </Modal>
    <Modal v-model="isShowUpdateBankModal" title="แก้ไขข้อมูลธนาคาร" class="modal-md">
      <UpdateBankForm :status="updateBank.status.value" @submit="updateBank.run" />
    </Modal>
    <Modal v-model="isShowAddPromotionModal" title="แอดโปรโมชั่น" class="modal-md">
      <AddPromotionForm :status="addPromotion.status.value" @submit="addPromotion.run" />
    </Modal>
    <Modal
      v-model="isShowAddCreditModal"
      class="modal-md"
      :no-backdrop-close="true"
      title="เพิ่มเครดิต"
    >
      <CreditForm :status="creditPlus.status.value" @submit="creditPlus.run" />
    </Modal>
    <Modal
      v-model="isShowRemoveCreditModal"
      class="modal-md"
      :no-backdrop-close="true"
      title="ลบเครดิต"
    >
      <CreditForm :status="creditMinus.status.value" @submit="creditMinus.run" />
    </Modal>
    <div class="flex justify-between items-center">
      <PageHeader
        :title="`ผู้เล่น: ${player.findItem!.first_name} ${player.findItem!.last_name}`"
      />
      <div class="flex items-center space-x-4">
        <Button class="btn-warning btn-sm" @click="isShowChangePassModal = true">
          เปลี่ยนรหัสผ่าน
        </Button>
        <Button class="btn-info btn-sm" @click="isShowUpdateBankModal = true">
          แก้ไขข้อมูลธนาคาร
        </Button>
        <Button class="btn-primary btn-sm" @click="isShowAddCreditModal = true">เพิ่มเครดิต</Button>
        <Button class="btn-danger btn-sm" @click="isShowRemoveCreditModal = true">ลบเครดิต</Button>
        <Button class="btn-success btn-sm" @click="isShowAddPromotionModal = true">
          แอดโปรโมชั่น
        </Button>
        <Button class="btn-danger btn-sm" @click="onKickPromotion">เตะโปรโมชั่น</Button>
      </div>
    </div>
    <div class="border-t border-gray-100 card">
      <Loader :is-loading="player.findStatus.isLoading">
        <dl class="divide-y divide-gray-100">
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">ชื่อ</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <p>{{ playerData.title }} {{ playerData.first_name }} {{ playerData.last_name }}</p>
              <p class="text-gray text-sm">{{ playerData.username }}</p>
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">เบอร์โทร</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {{ playerData.tel }}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">เครดิต</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {{ StringHelper.withComma(playerData.credit) }}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">เครดิตฟรี</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {{ StringHelper.withComma(playerData.credit_free) }}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">เครดิตค้าง</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {{ StringHelper.withComma(playerData.credit_hold) }}
            </dd>
          </div>
          <div
            v-if="playerData.birthdate"
            class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
          >
            <dt class="text-sm font-medium text-gray-900">วันเกิด</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {{ TimeHelper.getDateFormTime(playerData.birthdate) }}
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">ธนาคาร</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <div class="flex items-center">
                <img
                  :src="playerData.bank.image_url"
                  :title="playerData.bank.name_th"
                  class="w-[50px] h-[50px] object-cover mr-2"
                />
                <div>
                  <p class="text-gray text-xs">
                    {{ playerData.bank.name_th }}
                  </p>
                  <p class="font-bold">
                    {{ playerData.title }} {{ playerData.first_name }} {{ playerData.last_name }}
                  </p>
                  <p class="text-xs">{{ playerData.bank_number }}</p>
                </div>
              </div>
            </dd>
          </div>
          <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-900">เข้าสู่ระบบล่าสุด</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              {{ TimeHelper.getDateTimeFormTime(playerData.last_active) }}
            </dd>
          </div>
        </dl>
      </Loader>
    </div>
    <div class="mt-6">
      <Tab v-slot="{ option }" :options="tabOptions">
        <div class="p-4">
          <div v-if="option.value === 'deposits'">
            <AgentDepositTable :deposit="useAgentDepositByPlayerPageLoader()" />
          </div>
          <div v-if="option.value === 'withdraws'">
            <AgentWithdrawTable :withdraw="useAgentWithdrawByPlayerPageLoader()" />
          </div>
          <div v-if="option.value === 'game-transactions'">
            <AgentTransactionTable :transaction="useAgentGameTransactionByPlayerPageLoader()" />
          </div>
          <div v-if="option.value === 'redeem-histories'">
            <AgentRedeemHistoryTable :redeem="useAgentRedeemHistoryByPlayerPageLoader()" />
          </div>
          <div v-if="option.value === 'promotion'">
            <AgentPromotionRedeemHistoryTable
              :loader="useAgentPromotionRedeemHistoryPageLoader()"
            />
          </div>
        </div>
      </Tab>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  useAgentPlayerCreditMinusLoader,
  useAgentPlayerCreditPlusLoader,
  useAgentPlayerPageLoader,
  useAgentPromotionRedeemHistoryAddLoader,
  useAgentPromotionRedeemHistoryDeleteLoader,
  useAgentPromotionRedeemHistoryPageLoader,
} from '#imports'
import { IPlayerItem } from '~/models/player'
import AgentDepositTable from '~/containers/AgentDepositTable/index.vue'
import AgentRedeemHistoryTable from '~/containers/AgentRedeemHistoryTable/index.vue'
import AgentPromotionRedeemHistoryTable from '~/containers/AgentPromotionRedeemHistoryTable/index.vue'
import AgentWithdrawTable from '~/containers/AgentWithdrawTable/index.vue'
import { useAgentDepositByPlayerPageLoader } from '~/loaders/agentDeposit'
import { useAgentWithdrawByPlayerPageLoader } from '~/loaders/agentWithdraw'
import { useAgentRedeemHistoryByPlayerPageLoader } from '~/loaders/agentEarnpoint'
import AgentTransactionTable from '~/containers/AgentGameTransactionTable/index.vue'
import { useAgentGameTransactionByPlayerPageLoader } from '~/loaders/agentGame'
import ChangePassForm from '~/features/agent/AgentPlayerSingle/ChangePassForm.vue'
import UpdateBankForm from '~/features/agent/AgentPlayerSingle/UpdateBankForm.vue'
import {
  useAgentPlayerChangePasswordLoader,
  useAgentPlayerUpdateBankLoader,
} from '~/loaders/player'
import CreditForm from '~/features/agent/AgentPlayerSingle/CreditForm.vue'
import AddPromotionForm from '~/features/agent/AgentPlayerSingle/AddPromotionForm.vue'
import { ITabOptions } from '~/components/Tab/types'

const player = useAgentPlayerPageLoader()
const dialog = useDialog()
const playerData = computed<IPlayerItem>(() => player.findItem!)

const changePass = useAgentPlayerChangePasswordLoader(playerData.value.id)
const updateBank = useAgentPlayerUpdateBankLoader(playerData.value.id)
const creditPlus = useAgentPlayerCreditPlusLoader(playerData.value.id)
const creditMinus = useAgentPlayerCreditMinusLoader(playerData.value.id)
const kickPromotion = useAgentPromotionRedeemHistoryDeleteLoader(playerData.value.id)
const addPromotion = useAgentPromotionRedeemHistoryAddLoader(playerData.value.id)
const isShowChangePassModal = ref(false)
const isShowUpdateBankModal = ref(false)
const isShowAddCreditModal = ref(false)
const isShowRemoveCreditModal = ref(false)
const isShowAddPromotionModal = ref(false)

const tabOptions = computed<ITabOptions>(() => ({
  tabs: [
    {
      label: 'รายการฝาก',
      value: 'deposits',
    },
    {
      label: 'รายการถอน',
      value: 'withdraws',
    },
    {
      label: 'ประวัติการเล่นเกม',
      value: 'game-transactions',
    },
    {
      label: 'ประวัติการแลกของรางวัล',
      value: 'redeem-histories',
    },
    {
      label: 'ประวัติโปรโมชั่น',
      value: 'promotion',
    },
  ],
}))

const onKickPromotion = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการเตะโปรโมชั่นหรือไม่?',
      isShowCancelBtn: true,
    })
    .then(() => {
      kickPromotion.run()
    })
}

useWatchTrue(
  () => changePass.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'เปลี่ยนรหัสผ่านสำเร็จ',
      })
      .then(() => {
        isShowChangePassModal.value = false
      })
  }
)

useWatchTrue(
  () => changePass.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(changePass.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => updateBank.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขข้อมูลธนาคารสำเร็จ',
      })
      .then(() => {
        isShowUpdateBankModal.value = false
        player.find(playerData.value.id)
      })
  }
)

useWatchTrue(
  () => updateBank.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(updateBank.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => creditPlus.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'เพิ่มเครดิตสำเร็จ',
      })
      .then(() => {
        isShowAddCreditModal.value = false
        player.find(playerData.value.id)
      })
  }
)

useWatchTrue(
  () => creditPlus.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(creditPlus.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => creditMinus.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ลบเครดิตสำเร็จ',
      })
      .then(() => {
        isShowRemoveCreditModal.value = false
        player.find(playerData.value.id)
      })
  }
)

useWatchTrue(
  () => creditMinus.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(creditMinus.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => kickPromotion.status.value.isSuccess,
  () => {
    dialog.success({
      title: 'สำเร็จ',
      message: 'เตะโปรโมชั่นสำเร็จ',
    })
  }
)

useWatchTrue(
  () => kickPromotion.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(kickPromotion.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => addPromotion.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แอดโปรโมชั่นสำเร็จ',
      })
      .then(() => {
        isShowAddPromotionModal.value = false
      })
  }
)

useWatchTrue(
  () => addPromotion.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(addPromotion.status.value.errorData),
    })
  }
)
</script>
