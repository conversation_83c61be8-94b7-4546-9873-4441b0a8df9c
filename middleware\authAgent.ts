import { defineNuxtRouteMiddleware, navigateTo } from '#app'
import { useLogout, useMe } from '~/hooks/useAuth'
import { agentMenu, filterMenuByPermissions } from '~/constants/routes'
import { IUserRole } from '~/models/user'
import { CONFIG } from '~/constants/config'

export default defineNuxtRouteMiddleware(async () => {
  if (process.client) {
    return
  }

  const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)
  const me = useMe()
  const app = useApp()
  const logout = useLogout()

  if (!token.value) {
    return navigateTo(routes.login.to)
  }

  await me.run(undefined, { expire: 30 * 1000 })

  if (![IUserRole.AGENT, IUserRole.SUPER_AGENT].includes(me.data?.role as any)) {
    return abortNavigation({
      statusCode: 403,
      statusMessage: 'You are not authorized to access this page.',
    })
  }

  if (me.data?.role === IUserRole.SUPER_AGENT) {
    app.updateSidebar(agentMenu)
  } else {
    app.updateSidebar(agentMenu.map(filterMenuByPermissions(me.data?.permission ?? [])))
  }

  if (me.status.isError) {
    await logout.run()

    return navigateTo(routes.login.to)
  }
})
