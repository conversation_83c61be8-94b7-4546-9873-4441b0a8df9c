<template>
  <Form @submit="onSubmit">
    <FormFields :form="form" :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { useAgentPageStore } from '~/loaders/agent'
import { IUserRole } from '~/models/user'
import { userRolesAgent } from '~/constants/roles'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const agent = useAgentPageStore()

const form = useForm<
  Partial<{
    username: string
    password: string
    full_name: string
    role: IUserRole
  }>
>({
  initialValues: {
    role: IUserRole.AGENT,
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'full_name',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Username',
      name: 'username',
      prependIcon: defineComponent({
        setup() {
          return () => <p class="font-bold lowercase">{agent.findItem?.prefix}_</p>
        },
      }),
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'password',
      name: 'password',
      rules: yup.string().min(8).required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ตำแหน่ง',
      name: 'role',
      rules: yup.string().required(),
      options: userRolesAgent,
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
