<template>
  <div
    :class="[
      'sidebar',
      {
        'translate-x-0 ease-out': sidebarOpen,
        '-translate-x-full ease-in lg:ease-out lg:translate-x-0': !sidebarOpen,
      },
    ]"
  >
    <div class="flex items-center justify-center mt-8">
      <div class="flex items-center">
        <span class="text-white text-2xl mx-2 font-semibold">Dashboard</span>
      </div>
    </div>

    <nav class="mt-10 px-4">
      <template v-for="item in app.sidebar" :key="item.name">
        <nuxt-link
          v-if="!item.children"
          :class="[
            'flex items-center mt-2 py-2 px-4 text-gray-100 rounded-md hover:bg-gray-700 hover:text-white',
            {
              'sidebar-item': item.to !== '/',
              'sidebar-item-root': item.to === '/',
              'router-link-active': route.path.startsWith(item.to),
            },
          ]"
          :to="item.to"
        >
          <span>{{ item.name }}</span>
        </nuxt-link>
        <div v-else>
          <p class="text-gray-400 text-sm mt-4 font-bold">{{ item.name }}</p>
          <div class="ml-1">
            <nuxt-link
              v-for="child in item.children"
              :key="child.name"
              :class="[
                'flex items-center mt-2 py-2 px-4 text-gray-100 rounded-md hover:bg-gray-700 hover:text-white',
                {
                  'sidebar-item': child.to !== '/',
                  'sidebar-item-root': child.to === '/',
                  'router-link-active': route.path.startsWith(child.to),
                },
              ]"
              :to="child.to"
            >
              <span>{{ child.name }}</span>
            </nuxt-link>
          </div>
        </div>
      </template>
    </nav>
  </div>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'

defineProps<{
  sidebarOpen: boolean
}>()

const emit = defineEmits<{
  (e: 'sidebarOpen'): void
}>()

const app = useApp()
const route = useRoute()
</script>
