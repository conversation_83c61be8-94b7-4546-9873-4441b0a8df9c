<template>
  <div :title="item.title || item.value" class="px-4 flex whitespace-nowrap">
    <Badge
      :class="{
        'bg-success': item.value === 'approved' || item.value === 'settled',
        'bg-danger': item.value === 'rejected',
        'bg-warning': item.value === 'pending' || item.value === 'bet',
        'bg-dark': !statusList.includes(item.value),
      }"
    >
      {{ getValue }}
    </Badge>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue'
import { IRowItem } from '../types'
import { computed } from '#imports'

const statusList = ['approved', 'settled', 'rejected', 'pending', 'bet']

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem>,
    required: true,
  },
})

const getValue = computed(() => {
  return props.item.value
})
</script>
