<template>
  <Form @submit="onSubmit">
    <FormFields :form="form" :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      เพิ่ม
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IAgentProviderItem } from '~/models/provider'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item: IAgentProviderItem
}>()

const form = useForm<Partial<IAgentProviderItem>>({
  initialValues: props.item,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.STATIC,
    props: {
      label: 'ค่ายเกม',
      name: 'provider.name',
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'รูป',
      name: 'image_url',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ลำดับ',
      name: 'no',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เปอเซนต์',
      name: 'percent',
      placeholder: `เปอเซนต์สูงสุด ${props.item.provider.percent}%`,
      rules: yup.number().integer().min(1).max(props.item.provider.percent).required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'สถานะ',
      name: 'status',
      options: [
        {
          label: 'เปิดใช้งาน',
          value: 'active',
        },
        {
          label: 'ปรับปรุง',
          value: 'pending',
        },
        {
          label: 'ปิดใช้งาน',
          value: 'inactive',
        },
      ],
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'เปิดใช้งาน',
      name: 'is_enabled',
      rules: yup.boolean().required(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    percent: Number(data.percent),
    no: Number(data.no),
  })
})
</script>
