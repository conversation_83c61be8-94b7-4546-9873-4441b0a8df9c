<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentRedeemHistories />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentRedeemHistories from '~/features/agent/AgentRedeemHistories/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentRedeemHistory.name })
app.updatePageMeta({
  title: routes.agentRedeemHistory.name,
  breadcrumbs: [routes.agentRedeemHistory],
})
</script>
