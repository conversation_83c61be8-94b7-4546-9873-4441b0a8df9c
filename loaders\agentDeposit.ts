import { defineStore } from 'pinia'
import { useRequestOptions } from '~/hooks/useRequestOptions'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IAgentDepositItem } from '~/models/deposit'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useAgentDepositPageLoader = defineStore('agent.deposits.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IAgentDepositItem>({
    baseURL: 'agent/deposits',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentDepositApproveLoader = (depositId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<any>({
    method: 'POST',
    url: `agent/deposits/${depositId}/approved`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentDepositRejectLoader = (depositId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<any, { remark?: string }>({
    method: 'POST',
    url: `agent/deposits/${depositId}/rejected`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentDepositByPlayerPageLoader = defineStore('agent.deposits_player.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()
  const route = useRoute()

  return usePageLoader<IAgentDepositItem>({
    baseURL: 'agent/deposits?player=' + route.params.id,
    getBaseRequestOptions: () => {
      return {
        ...getDefaultWithAuth(),
        params: {
          limit: 10,
        },
      }
    },
  })
})
