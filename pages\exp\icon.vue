<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Icon" />
    <div class="mx-auto bg-white p-8 rounded-10 grid grid-cols-4 gap-6">
      <div
        v-for="(icon, index) in icons"
        :key="index"
        class="flex flex-col cursor-pointer"
        title="Copy to clipboard"
        @click="() => onCopy(icon)"
      >
        <i :class="`ic ic-${icon} w-10 mx-auto mb-2`" />
        <code class="bg-gray-fill text-sm text-center rounded px-2">.ic ic-{{ icon }}</code>
      </div>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'
import { StringHelper } from '~/utils/StringHelper'
import { useNotification } from '~/hooks/useNotification'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.icon.name })
app.updatePageMeta({
  title: styleguideMenu.icon.name,
  breadcrumbs: [styleguideMenu.icon],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))
const noti = useNotification()
const icons = [
  'chevron-up-solid',
  'chevron-down-solid',
  'chevron-left-solid',
  'chevron-right-solid',
  'bell-solid',
  'bell-outline',
  'archive-solid',
  'archive-outline',
  'building-solid',
  'building-outline',
  'check-circle-solid',
  'check-circle-outline',
  'info-circle-solid',
  'info-circle-outline',
  'plus-circle-solid',
  'plus-circle-outline',
  'user-circle-solid',
  'user-circle-outline',
  'x-circle-solid',
  'x-circle-outline',
  'file-arrow-up-solid',
  'file-arrow-up-outline',
  'file-arrow-down-solid',
  'file-arrow-down-outline',
  'file-earmark-up-solid',
  'file-earmark-up-outline',
  'file-earmark-down-solid',
  'file-earmark-down-outline',
  'file-check-circle-solid',
  'file-check-circle-outline',
  'file-signature-solid',
  'folder-solid',
  'folder-outline',
  'folder-open-solid',
  'gear-solid',
  'gear-outline',
  'house-solid',
  'house-outline',
  'inbox-solid',
  'inbox-outline',
  'arrow-clockwise-solid',
  'search-solid',
  'send-solid',
  'send-outline',
  'pen-path-solid',
  'pen-square-solid',
  'people-solid',
  'qr-code-solid',
  'qr-code-scan-solid',
  'shield-lock-solid',
  'shield-lock-outline',
  'sliders-solid',
  'table-solid',
  'table-column-solid',
  'trash-solid',
  'trash-outline',
  'exclamation-triangle-solid',
  'exclamation-triangle-outline',
  'user-solid',
  'user-outline',
  'x-mark-solid',
  'triangle-solid',
  'triangle-outline',
  'star-solid',
  'star-outline',
  'circle-solid',
  'circle-outline',
  'file-earmark-text-solid',
  'file-earmark-text-outline',
  'window-stack-outline',
  'menu-button-solid',
  'menu-button-outline',
  'card-list-outline',
  'exclamation-square-solid',
  'exclamation-square-outline',
  'bar-chart-steps-outline',
  'collection-solid',
  'collection-outline',
  'user-card-solid',
  'user-card-outline',
  'users-group-solid',
  'user-shield-solid',
  'check-solid',
  'check-all-solid',
  'landmark-outline',
  'briefcase-solid',
  'briefcase-outline',
  'file-earmark-solid',
  'file-earmark-outline',
  'user-tie-solid',
  'envelope-solid',
  'envelope-outline',
  'eye-solid',
  'eye-outline',
  'eye-slash-solid',
  'eye-slash-outline',
  'zoom-in-solid',
  'zoom-out-solid',
  'file-circle-minus-solid',
  'person-gear-solid',
  'person-gear-outline',
  'diagram-3-solid',
  'diagram-3-outline',
  'file-earmark-check-solid',
  'file-earmark-check-outline',
]

const onCopy = (icon: string) => {
  StringHelper.copyToClipBoard(`ic ic-${icon}`)
  noti.success({
    title: 'Copied',
    text: `ic ic-${icon}`,
  })
}
</script>
