<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import { useAgentPageStore } from '~/loaders/agent'
import AgentSingle from '~/features/AgentSingle/index.vue'

const app = useApp()
const route = useRoute()
const agent = useAgentPageStore()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_ADMIN,
    async (to) => {
      const agent = useAgentPageStore()

      await agent.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(agent.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.agentEdit(route.params.id as string, agent.findItem?.name)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.agents, nav],
})
</script>
