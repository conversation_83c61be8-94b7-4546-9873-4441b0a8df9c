<template>
  <div>
    <a
      v-if="item.props?.isNewTab"
      :title="item.title || item.value"
      target="_blank"
      class="px-4 block whitespace-nowrap"
      :href="item.props?.href"
    >
      <div v-html="getValue || '-'" />
    </a>
    <nuxt-link
      v-else
      :title="item.title || item.value"
      class="px-4 block whitespace-nowrap"
      :href="item.props?.href"
    >
      <div v-html="getValue || '-'" />
    </nuxt-link>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue'
import { IRowItem } from '../types'
import { StringHelper } from '~/utils/StringHelper'
import { computed } from '#imports'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ max?: number; href: string; isNewTab: boolean }>>,
    required: true,
  },
})

const getValue = computed(() => {
  return props.item.props?.max
    ? StringHelper.truncate(props.item.value, props.item.props.max)
    : props.item.value
})
</script>
