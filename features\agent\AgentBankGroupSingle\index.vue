<template>
  <div>
    <PageHeader :title="routes.agentBankGroups.name" />
    <Table :options="tableOptions" @pageChange="bank.fetch" @search="bank.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useAgentBankPageLoader } from '~/loaders/agentBank'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import Bank from '~/features/agent/AgentBankGroupSingle/Bank.vue'
import { IBankItem } from '~/models/bank'
import Switcher from '~/features/agent/AgentBankGroupSingle/Switcher.vue'
import { useAgentBankGroupToggle } from '~/loaders/agentBankGroupToggle'

const dialog = useDialog()
const route = useRoute()
const bank = useAgentBankPageLoader()
const toggle = useAgentBankGroupToggle()

bank.setFetchLoading()
onMounted(() => {
  bank.fetch(1, '', {
    params: {
      limit: 1000,
      compare_to_group: route.params.id as string,
    },
  })
})

useWatchTrue(
  () => toggle.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(toggle.status.value.errorData),
    })
  }
)

const tableOptions = useTable<IBankItem>({
  repo: bank,
  columns: () => [
    {
      value: 'บัญชี',
    },
    {
      value: 'ใช้งาน',
    },
    {
      value: 'ตั้งค่าบัญชีหลัก',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          type: COLUMN_TYPES.COMPONENT,
          className: 'p-3',
          props: {
            bank: item,
          },
          value: Bank,
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          props: {
            action: async (enabled: boolean) => {
              await toggle.run({ id: route.params.id as string, agent_bank_id: item.id, enabled })
            },
            value: item.is_in_group,
          },
          value: Switcher,
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          props: {
            action: async (enabled: boolean) => {
              await toggle.run({
                id: route.params.id as string,
                agent_bank_id: item.id,
                enabled,
                is_default: true,
              })
            },
            value: item.is_default,
          },
          value: Switcher,
        },
      ]
    }),
})
</script>
