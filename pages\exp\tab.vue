<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Tab" />
    <Tab v-slot="{ option }" :options="tabOptions">
      <div v-if="option.value === 'info'">
        <div class="p-4 border-b border-b-gray-border">Test Tab - row 1</div>
        <div class="p-4">Test Tab - row 2</div>
      </div>
      <div v-else class="p-8">
        นี่คือ Tab Content ของ <span class="text-info">{{ option.label }}</span>
      </div>
    </Tab>
  </NuxtLayout>
</template>

<script lang="tsx" setup>
import { computed } from '#imports'
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'
import { ITabOptions } from '~/components/Tab/types'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.tab.name })
app.updatePageMeta({
  title: styleguideMenu.tab.name,
  breadcrumbs: [styleguideMenu.tab],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))

const tabOptions = computed<ITabOptions>(() => ({
  tabs: [
    {
      label: 'ข้อมูลทั่วไป',
      value: 'info',
      icon: 'user-solid',
    },
    {
      label: 'ข้อมูลติดต่อ',
      value: 'contact',
      icon: 'user-card-solid',
    },
    {
      label: 'นามแฝง',
      value: 'alias',
      icon: 'user-circle-solid',
    },
    {
      label: 'สมาชิกกลุ่ม',
      value: 'group',
      icon: 'users-group-solid',
    },
    {
      label: 'สิทธิ์บทบาท',
      value: 'role',
      icon: 'user-shield-solid',
    },
  ],
}))
</script>
