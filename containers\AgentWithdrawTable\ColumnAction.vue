<template>
  <div v-if="item.props?.item.status === 'pending'" class="flex items-center space-x-3">
    <Modal v-model="isShowModal" title="ปฏิเสธการถอนงิน" class="modal-sm">
      <Form @submit="onRejectSubmit">
        <FormFields :options="fields" :form="form" />
        <Button class="btn-danger w-full" type="submit" :is-loading="reject.status.value.isLoading">
          Reject
        </Button>
      </Form>
    </Modal>
    <Button
      class="btn-success btn-sm"
      icon="check-circle-solid"
      :is-only-icon="true"
      :is-loading="approve.status.value.isLoading"
      @click="onApprove"
    />
    <Button
      class="btn-danger btn-sm"
      icon="x-circle-solid"
      :is-only-icon="true"
      :is-loading="reject.status.value.isLoading"
      @click="isShowModal = true"
    />
  </div>
</template>
<script lang="tsx" setup>
import { PropType } from 'vue'
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { IRowItem } from '~/components/Table/types'
import { INPUT_TYPES } from '~/components/Form/types'
import { IAgentWithdrawItem } from '~/models/withdraw'
import {
  useAgentWithdrawApproveLoader,
  useAgentWithdrawRejectLoader,
} from '~/loaders/agentWithdraw'
import { useDialog } from '~/hooks/useDialog'
import { createFormFields } from '~/hooks/useForm'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ item: IAgentWithdrawItem; onDone: () => void }>>,
    required: true,
  },
})

const approve = useAgentWithdrawApproveLoader(props.item.props!.item.id)
const reject = useAgentWithdrawRejectLoader(props.item.props!.item.id)
const dialog = useDialog()
const isShowModal = ref(false)
const form = useForm()

const fields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      label: 'หมายเหตุ',
      name: 'remark',
      rows: 5,
      rules: yup.string().required(),
    },
  },
])

const onApprove = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการอนุมัติการถอนงินนี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      approve.run({})
    })
}

const onRejectSubmit = form.handleSubmit((values) => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการปฏิเสธการถอนเงินนี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      reject.run(values)
    })
})

useWatchTrue(
  () => approve.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'อนุมัติการถอนเงินสำเร็จ',
      })
      .then(() => {
        props.item.props!.onDone()
      })
  }
)

useWatchTrue(
  () => approve.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(approve.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => reject.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ปฏิเสธการถอนเงินสำเร็จ',
      })
      .then(() => {
        props.item.props!.onDone()
      })
  }
)

useWatchTrue(
  () => reject.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(reject.status.value.errorData),
    })
  }
)
</script>
