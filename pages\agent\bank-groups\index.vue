<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentBankGroups />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentBankGroups from '~/features/agent/AgentBankGroups/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentBankGroups.name })
app.updatePageMeta({
  title: routes.agentBankGroups.name,
  breadcrumbs: [routes.agentBankGroups],
})
</script>
