<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentInfo />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentInfo from '~/features/agent/AgentInfo'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentInfo.name })
app.updatePageMeta({
  title: routes.agentInfo.name,
  breadcrumbs: [routes.agentInfo],
})
</script>
