<template>
  <FieldWrapper v-bind="wrapperProps">
    <ClientOnly>
      <QuillEditor
        v-model:content="value"
        :placeholder="placeholder ?? props.label"
        :autofocus="autoFocus"
        :disabled="disabled"
        :name="name"
        class="border-1"
        content-type="html"
        :options="options"
      />
    </ClientOnly>
  </FieldWrapper>
</template>
<script lang="ts" setup>
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { IWYSIWYGFieldProps } from '~/components/Form/wysiwyg_field.types'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

const props = defineProps<IWYSIWYGFieldProps>()

const { value, label, wrapperProps, onChange, fieldClassName, disabled, validate } =
  useFieldHOC<string>(props)

const options = {
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'], // toggled buttons
      ['blockquote', 'code-block'],

      [{ header: 1 }, { header: 2 }], // custom button values
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
      [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
      [{ direction: 'rtl' }], // text direction

      [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
      [{ header: [1, 2, 3, 4, 5, 6, false] }],

      [{ color: [] }, { background: [] }], // dropdown with defaults from theme
      [{ font: [] }],
      [{ align: [] }],

      ['clean'], // remove formatting button
    ],
  },
  theme: 'snow',
}
</script>
