<template>
  <div class="flex flex-col items-center border border-gray-200 bg-white px-3 py-2">
    <img class="w-16 h-8 mr-4 object-contain" :src="game.image_url" :alt="game.name" />
    <div class="font-semibold text-md">
      {{ game.name }}
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { PropType } from 'vue'

defineProps({
  game: {
    type: Object as PropType<any>,
    required: true,
  },
})
</script>
