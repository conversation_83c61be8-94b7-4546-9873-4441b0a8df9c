import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'

export interface IGameConnection {
  id: string
  name: string
  slug: string
  connection: any
  created_at: string
  updated_at: string
}

export const useGameConnectionPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IGameConnection>({
    baseURL: '/admin/game-connections',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useGameConnectionPageStore = defineStore(
  'useGameConnectionPage',
  useGameConnectionPageLoader
)
