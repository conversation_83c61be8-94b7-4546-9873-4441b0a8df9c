import { defineNuxtRouteMiddleware, navigateTo } from '#app'
import { useLogout, useMe } from '~/hooks/useAuth'
import { CONFIG } from '~/constants/config'
import { adminMenu, agentMenu } from '~/constants/routes'
import { IUserRole } from '~/models/user'

export default defineNuxtRouteMiddleware(async () => {
  const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)

  const me = useMe()
  const app = useApp()
  const logout = useLogout()

  if (!token.value) {
    return navigateTo(routes.login.to)
  }

  await me.run(undefined, { expire: 30 * 1000 })

  if (me.data?.role === IUserRole.SUPER_ADMIN) {
    app.updateSidebar(adminMenu)
  } else {
    app.updateSidebar(agentMenu)
  }

  if (me.status.isError) {
    await logout.run()

    return navigateTo(routes.login.to)
  }
})
