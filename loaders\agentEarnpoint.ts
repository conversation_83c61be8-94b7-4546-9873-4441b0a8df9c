import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IEarnPointItem, type IRedeemHistoryItem } from '~/models/earnPoint'
import { useRequestOptions } from '~/hooks/useRequestOptions'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useAgentEarnPointPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IEarnPointItem>({
    baseURL: '/agent/rewards',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentRedeemHistoryPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IRedeemHistoryItem>({
    baseURL: '/agent/reward-transactions',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentRedeemHistoryApproveLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<any, { id: string }>({
    method: 'POST',
    url: '/agent/reward-transactions/:id/approve',
    getURL(data, opts) {
      return `/agent/reward-transactions/${data?.id}/approve`
    },
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentRedeemHistoryRejectLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<any, { id: string }>({
    method: 'POST',
    url: '/agent/reward-transactions/:id/reject',
    getURL(data, opts) {
      return `/agent/reward-transactions/${data?.id}/reject`
    },
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentRedeemHistoryByPlayerPageLoader = defineStore(
  'agent.redeem_history_player.page',
  () => {
    const route = useRoute()
    const { getDefaultWithAuth } = useRequestOptions()

    return usePageLoader<IRedeemHistoryItem>({
      baseURL: '/agent/reward-transactions?player_id=' + route.params.id,
      getBaseRequestOptions: () => {
        return {
          ...getDefaultWithAuth(),
          params: {
            limit: 10,
          },
        }
      },
    })
  }
)
