import { useObjectLoader } from '~/lib/api/loaderObject'

export interface IWheelConfigSetting {
  wheel_id: string
  amount: number
  name: string
  image?: string
  weight: number
}

export interface IWheelConfig {
  settings: IWheelConfigSetting[]
  price: number
  limit: number
}

export const useAgentWheelConfigLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IWheelConfig>({
    method: 'GET',
    url: '/agent/wheels',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentWheelConfigUpdateLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IWheelConfig>({
    method: 'PUT',
    url: '/agent/wheels',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
