<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      {{ item ? 'แก้ไข' : 'สร้าง' }}
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IEarnPointItem } from '~/models/earnPoint'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item?: IEarnPointItem
}>()

const form = useForm<Partial<IEarnPointItem>>({
  initialValues: props.item || {},
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'title',
      label: 'หัวข้อ',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.WYSIWYG,
    props: {
      name: 'description',
      label: 'description',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      name: 'image_url',
      label: 'รูป',
      accept: 'jpg,png,jpeg,gif,wepb',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'credit',
      label: 'credit fee',
      rules: yup.string().numeric(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'point',
      label: 'point',
      rules: yup.string().numeric().required(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  const type = Number(data.credit) > 0 ? 'credit' : 'reward'

  emits('submit', {
    ...data,
    type,
    point: Number(data.point),
    credit: Number(data.credit),
  })
})
</script>
