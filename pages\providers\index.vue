<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <Providers />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import Providers from '~/features/Providers/index.vue'

const app = useApp()

definePageMeta({
  middleware: [MIDDLEWARES.AUTH_ADMIN],
})

app.updateDocMeta({ title: routes.providers.name })
app.updatePageMeta({
  title: routes.providers.name,
  breadcrumbs: [routes.providers],
})
</script>
