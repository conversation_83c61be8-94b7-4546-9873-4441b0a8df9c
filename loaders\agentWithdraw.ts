import { defineStore } from 'pinia'
import { useRequestOptions } from '~/hooks/useRequestOptions'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IAgentWithdrawItem } from '~/models/withdraw'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useAgentWithdrawPageLoader = defineStore('agent.withdraws.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IAgentWithdrawItem>({
    baseURL: 'agent/withdraws',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentWithdrawByPlayerPageLoader = defineStore('agent.withdraws_player.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()
  const route = useRoute()

  return usePageLoader<IAgentWithdrawItem>({
    baseURL: 'agent/withdraws?player=' + route.params.id,
    getBaseRequestOptions: () => {
      return {
        ...getDefaultWithAuth(),
        params: {
          limit: 10,
        },
      }
    },
  })
})

export const useAgentWithdrawApproveLoader = (withdrawId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<any>({
    method: 'POST',
    url: `agent/withdraws/${withdrawId}/approved`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentWithdrawRejectLoader = (withdrawId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<any, { remark?: string }>({
    method: 'POST',
    url: `agent/withdraws/${withdrawId}/rejected`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
