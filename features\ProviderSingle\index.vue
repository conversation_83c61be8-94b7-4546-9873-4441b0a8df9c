<template>
  <div>
    <div class="flex justify-between items-center">
      <PageHeader :title="`รายละเอียด ${provider.findItem?.name}`" />
    </div>
    <div class="card p-6">
      <DetailForm
        :status="provider.updateStatus"
        :item="provider.findItem"
        @submit="onUpdateSubmit"
      />
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { useProviderPageStore } from '~/loaders/provider'
import DetailForm from '~/features/ProviderSingle/DetailForm.vue'
import { IProviderItem } from '~/models/provider'

const route = useRoute()
const provider = useProviderPageStore()

const dialog = useDialog()
const onUpdateSubmit = (values: IProviderItem) => provider.update(route.params.id as string, values)

useWatchTrue(
  () => provider.updateStatus.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'บันทึกข้อมูลเรียบร้อย',
      })
      .then(() => {
        provider.find(route.params.id as string)
      })
  }
)

useWatchTrue(
  () => provider.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(provider.updateStatus.errorData),
    })
  }
)
</script>
