<template>
  <Loader :is-loading="permission.status.value.isLoading">
    <Form @submit="onSubmit">
      <FormFields class="md:grid-cols-3 grid gap-4" :form="form" :options="formFields" />
      <Button type="submit" class="btn-primary w-full btn-min mt-4" :is-loading="status.isLoading">
        {{ initialValues ? 'บันทึก' : 'สร้าง' }}
      </Button>
    </Form>
  </Loader>
</template>
<script lang="ts" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { useAgentPermissionListLoader } from '~/loaders/agentRole'
import { createFormFields } from '~/hooks/useForm'
import { IFormField, INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IPermissionItem } from '~/models/permission'
import { IPositionItem } from '~/models/position'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  initialValues?: IPositionItem
}>()

const permission = useAgentPermissionListLoader()
const form = useForm<
  Partial<{
    name: string
    slug: string
  }>
>()

permission.setLoading()
onMounted(() => {
  permission.run()
})

const transformPermissionToFormValue = (permission: IPermissionItem[] = []) => {
  const formValues = {}

  permission.forEach((item) => {
    formValues[`[permissions.${item.key}]`] = item.is_enabled
  })

  return formValues
}

watch(
  () => props.initialValues,
  () => {
    if (props.initialValues) {
      form.setValues({
        name: props.initialValues.name,
        slug: props.initialValues.slug,
        ...transformPermissionToFormValue(props.initialValues.permissions),
      })
    }
  },
  { deep: true, immediate: true }
)

useWatchTrue(
  () => permission.status.value.isSuccess,
  () => {
    if (!props.initialValues) {
      form.setValues(transformPermissionToFormValue(permission.items.value))
    }
  }
)

const onSubmit = form.handleSubmit((values) => {
  const permissions = []

  Object.keys(values).forEach((key) => {
    if (key.includes('permissions.')) {
      permissions.push({
        key: key.replace('permissions.', ''),
        is_enabled: values[key],
      })
    }
  })

  emits('submit', {
    name: values.name,
    slug: values.slug,
    permissions,
  })
})

const formFields = createFormFields(() => {
  const fields: IFormField[] = [
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'ชื่อ',
        name: 'name',
        rules: yup.string().required(),
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'slug',
        name: 'slug',
        isDisabled: !!props.initialValues,
        rules: yup.string().required(),
      },
    },
  ]

  permission.items.value.forEach((item, index) => {
    fields.push({
      type: INPUT_TYPES.TOGGLE_SWITCH,
      class: index === 0 ? 'col-start-1' : '',
      props: {
        isDisabled: !item.is_enabled,
        switchLabel: item.name,
        name: `[permissions.${item.key}]`,
      },
    })
  })

  return fields
})
</script>
