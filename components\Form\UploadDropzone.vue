<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      class="dropzone"
      :class="[{ 'bg-gray-fill': isDragover }, dropzoneClassName, fieldClassName]"
      @dragover="dragover"
      @dragleave="dragleave"
      @drop="drop"
    >
      <em
        v-if="selectedFile"
        title="delete"
        class="ic ic-trash-solid close-icon"
        @click="handleDeleteFile"
      />
      <input
        ref="fileInput"
        type="file"
        :accept="acceptFile"
        :disabled="disabled"
        @change="handleChange"
      />
      <div class="dropzone-wrapper">
        <div v-if="selectedFile" class="dropzone-preview">
          <div v-if="isImage(selectedFile)" class="preview-img">
            <img :src="generateURL(selectedFile)" alt="file" />
          </div>
          <div class="preview-file-name">
            <p>{{ selectedFile.name }}</p>
          </div>
          <div class="preview-file-info">{{ selectedFileSize + ' MB' }}</div>
        </div>
        <Button
          v-if="!selectedFile"
          class="btn-primary text-sm min-w-[150px]"
          @click="handleOpenFile"
        >
          เลือกไฟล์
        </Button>
        <div v-if="!selectedFile" class="dropzone-message">
          <p>หรือลากวางไฟล์ของคุณในกล่องนี้</p>
          <p v-if="acceptFileSize">ขนาดไม่เกิน: {{ (acceptFileSize / 1000).toFixed(2) }} MB</p>
        </div>
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="tsx" setup>
import { IUploadDropzoneProps } from './upload_dropzone.types'
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { computed } from '#imports'

const props = withDefaults(defineProps<IUploadDropzoneProps>(), {})
const emit = defineEmits(['change', 'delete'])

const {
  wrapperProps,
  fieldClassName,
  disabled,
  handleChange: onChange,
  setErrors,
  value,
} = useFieldHOC<File>(props)

const fileInput = ref<HTMLInputElement>()
const isDragging = ref<boolean>(false)
const isDragover = ref<boolean>(false)

const acceptFile = computed(() =>
  typeof props.accept === 'string' ? props.accept : props.accept?.join(',')
)

const acceptFileSize = computed(() => props.maxSize)
const selectedFileSize = computed(() => ((value?.value.size || 0) / 1000 / 1000).toFixed(2))
const selectedFile = computed(() => value?.value)

const handleChange = (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0]
  const result = handleCheckFileCondition(file)

  if (result) {
    onChange(file)
    emit('change', file)
  }
}

const handleOpenFile = () => {
  fileInput.value?.click()
}

const handleDeleteFile = () => {
  fileInput.value?.value && (fileInput.value.value = '')
  onChange(undefined)
  emit('delete')
}

const handleCheckFileCondition = (file: File | undefined): boolean => {
  if (!file) return false
  const accept = checkAcceptFile(file)

  if (!accept) {
    setErrors('File type is not supported')

    return false
  }

  const maxSize = checkMaxSize(file)

  if (!maxSize) {
    setErrors('File size is too large')

    return false
  }

  setErrors('')

  return true
}

const dragover = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
  isDragover.value = true
}

const dragleave = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  isDragover.value = false
}

const drop = (e: DragEvent) => {
  e.preventDefault()
  const file = e.dataTransfer?.files[0]
  const result = handleCheckFileCondition(file)

  isDragging.value = false
  isDragover.value = false

  if (result) {
    emit('change', file)
  }
}

const isImage = (file: File) => {
  return file.type.startsWith('image/')
}

const checkAcceptFile = (file: File): boolean => {
  const fileType = file.type.split('/')[1]

  return acceptFile.value ? acceptFile.value.includes(fileType) : true
}

const checkMaxSize = (file: File): boolean => {
  if (acceptFileSize.value) {
    return file.size / 1000 <= acceptFileSize.value
  }

  return true
}

const generateURL = (file: File) => {
  const fileSrc = URL.createObjectURL(file)

  setTimeout(() => {
    URL.revokeObjectURL(fileSrc)
  }, 1000)

  return fileSrc
}
</script>
