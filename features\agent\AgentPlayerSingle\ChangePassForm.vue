<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      เปลี่ยนรหัสผ่าน
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { IStatus } from '~/lib/api/types'
import { INPUT_TYPES } from '~/components/Form/types'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const form = useForm<Partial<{ new_password: string }>>()

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'Password',
      name: 'new_password',
      rules: yup.string().min(8).required(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
