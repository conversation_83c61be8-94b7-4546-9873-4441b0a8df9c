import { type AxiosRequestConfig } from 'axios'
import { useRuntimeConfig } from '#app'
import { CONFIG } from '~/constants/config'
import { useCookie7Days } from '~/hooks/useCookie'

export const useRequestOptions = () => {
  const config = useRuntimeConfig()

  const getMock = (): Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string } => {
    return {
      baseURL: config.public.baseAPIMock,
    }
  }

  const getDefault = (): Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string } => {
    return {
      baseURL: config.public.baseAPI,
    }
  }

  const getDefaultWithAuth = (): Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string } => {
    const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)

    return {
      baseURL: config.public.baseAPI,
      headers: {
        ...(token ? { Authorization: token.value } : {}),
      },
    }
  }

  return { getDefault, getDefaultWithAuth, getMock }
}
