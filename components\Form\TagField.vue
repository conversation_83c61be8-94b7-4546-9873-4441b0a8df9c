<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      :class="[
        fieldClassName,
        'pb-0',
        {
          disabled: disabled,
        },
      ]"
    >
      <VueMultiselect
        :disabled="disabled"
        :name="name"
        :options="options"
        :model-value="getValue"
        :multiple="true"
        :taggable="true"
        :close-on-select="false"
        :tag-placeholder="placeholder ?? props.label"
        :placeholder="placeholder ?? props.label"
        label="label"
        track-by="value"
        @update:modelValue="onTagChange"
        @tag="handleTag"
      />
    </div>
  </FieldWrapper>
</template>
<script lang="ts" setup>
import VueMultiselect from 'vue-multiselect'
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { ITagFieldProps } from '~/components/Form/tag_field.types'
import { IOption } from '~/components/Form/types'

const props = withDefaults(defineProps<ITagFieldProps>(), {})
const emits = defineEmits<{
  (event: 'change', value: string): void
}>()

const { value, wrapperProps, fieldClassName, disabled, handleChange } = useFieldHOC<any[]>(props)

const options = computed<IOption[]>(() =>
  ArrayHelper.toArray(value.value).map((item) => ObjectHelper.createOption(item, item))
)

const getValue = computed(() => {
  return value.value.map((item) => ObjectHelper.createOption(item, item))
})

const onTagChange = (e: IOption[]) => {
  value.value = ArrayHelper.toArray(e).map((item: IOption) => item.value)
}

const handleTag = (tag: string) => {
  handleChange(ArrayHelper.toArray(value.value).concat(tag))
  emits('change', tag)
}
</script>
