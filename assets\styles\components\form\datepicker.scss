.dp__input_wrap {
  position: relative;
  width: 100%;
  box-sizing: unset
}

.dp__input_wrap:focus {
  border-color: var(--dp-border-color-hover);
  outline: none
}

.dp__input {
  // TODO
}

.dp__input::placeholder {
  opacity: .7
}

.dp__input:hover {
  border-color: var(--dp-border-color-hover)
}

.dp__input_reg {
  caret-color: rgb(0 0 0 / 0%)
}

.dp__input_focus {
  border-color: var(--dp-border-color-hover)
}

.dp__disabled {
  background: var(--dp-disabled-color)
}

.dp__disabled::placeholder {
  color: var(--dp-disabled-color-text)
}

.dp__input_icons {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  stroke-width: 0;
  font-size: 1rem;
  line-height: 1.5rem;
  padding: 6px 12px;
  color: var(--dp-icon-color);
  box-sizing: content-box
}

.dp__input_icon {
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  color: var(--dp-icon-color)
}

.dp__clear_icon {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--dp-icon-color)
}

.dp__input_icon_pad {
  padding-left: 35px
}

.dp__input_valid {
  box-shadow: 0 0 4px var(--dp-success-color);
  border-color: var(--dp-success-color)
}

.dp__input_valid:hover {
  border-color: var(--dp-success-color)
}

.dp__input_invalid {
  box-shadow: 0 0 4px var(--dp-danger-color);
  border-color: var(--dp-danger-color)
}

.dp__input_invalid:hover {
  border-color: var(--dp-danger-color)
}

.dp__menu {
  position: absolute;
  background: var(--dp-background-color);
  border-radius: 4px;
  min-width: 260px;
  font-size: 1rem;
  user-select: none;
  border: 1px solid var(--dp-menu-border-color);
  box-sizing: border-box
}

.dp__menu::after {
  box-sizing: border-box
}

.dp__menu::before {
  box-sizing: border-box
}

.dp__menu:focus {
  border: 1px solid var(--dp-menu-border-color);
  outline: none
}

.dp__menu_index {
  z-index: 99999
}

.dp__menu_readonly, .dp__menu_disabled {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1
}

.dp__menu_disabled {
  background: rgb(255 255 255 / 50%);
  cursor: not-allowed
}

.dp__menu_readonly {
  background: rgb(0 0 0 / 0%);
  cursor: default
}

.dp__arrow_top {
  left: 50%;
  top: -1px;
  height: 12px;
  width: 12px;
  background-color: var(--dp-background-color);
  position: absolute;
  border-left: 1px solid var(--dp-menu-border-color);
  border-top: 1px solid var(--dp-menu-border-color);
  transform: translate(-50%, -50%) rotate(45deg)
}

.dp__arrow_bottom {
  left: 50%;
  bottom: -1px;
  height: 12px;
  width: 12px;
  background-color: var(--dp-background-color);
  position: absolute;
  border-right: 1px solid var(--dp-menu-border-color);
  border-bottom: 1px solid var(--dp-menu-border-color);
  transform: translate(-50%, 50%) rotate(45deg)
}

.dp__now_wrap {
  text-align: center;
  padding: 2px 0
}

.dp__now_button {
  border: 1px solid var(--dp-primary-color);
  color: var(--dp-primary-color);
  padding: 0 4px;
  font-weight: bold;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  background: rgb(0 0 0 / 0%)
}

.dp__preset_ranges {
  padding: 5px;
  border-right: 1px solid var(--dp-border-color)
}

.dp__sidebar_left {
  padding: 5px;
  border-right: 1px solid var(--dp-border-color)
}

.dp__sidebar_right {
  padding: 5px;
  border-left: 1px solid var(--dp-border-color)
}

.dp__preset_range {
  padding: 5px
}

.dp__preset_range:hover {
  background-color: var(--dp-hover-color);
  cursor: pointer
}

.dp__menu_content_wrapper {
  display: flex
}

.dp__calendar_wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  flex: 0
}

.dp__calendar_header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--dp-text-color);
  white-space: nowrap;
  font-weight: bold
}

.dp__calendar_header_item {
  text-align: center;
  flex-grow: 1;
  height: 35px;
  padding: 5px;
  width: 35px;
  box-sizing: border-box
}

.dp__calendar_row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 5px 0
}

.dp__calendar_item {
  text-align: center;
  flex-grow: 1;
  box-sizing: border-box;
  color: var(--dp-text-color)
}

.dp__calendar {
  position: relative
}

.dp__calendar_header_cell {
  border-bottom: thin solid var(--dp-border-color);
  padding: .5rem
}

.dp__cell_inner {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  border-radius: 4px;
  height: 35px;
  padding: 5px;
  width: 35px;
  border: 1px solid rgb(0 0 0 / 0%);
  box-sizing: border-box;
  position: relative
}

.dp__cell_auto_range_start, .dp__date_hover_start:hover, .dp__range_start {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0
}

.dp__cell_auto_range_end, .dp__date_hover_end:hover, .dp__range_end {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0
}

.dp__range_end, .dp__range_start, .dp__active_date {
  background: var(--dp-primary-color);
  color: var(--dp-primary-text-color)
}

.dp__cell_auto_range_end, .dp__cell_auto_range_start {
  border-top: 1px dashed var(--dp-primary-color);
  border-bottom: 1px dashed var(--dp-primary-color)
}

.dp__date_hover_end:hover, .dp__date_hover_start:hover, .dp__date_hover:hover {
  background: var(--dp-hover-color);
  color: var(--dp-hover-text-color)
}

.dp__cell_offset {
  color: var(--dp-secondary-color)
}

.dp__cell_disabled {
  color: var(--dp-secondary-color);
  cursor: not-allowed
}

.dp__range_between {
  background: var(--dp-hover-color);
  border-radius: 0;
  border-top: 1px solid var(--dp-hover-color);
  border-bottom: 1px solid var(--dp-hover-color)
}

.dp__range_between_week {
  background: var(--dp-primary-color);
  color: var(--dp-primary-text-color);
  border-radius: 0;
  border-top: 1px solid var(--dp-primary-color);
  border-bottom: 1px solid var(--dp-primary-color)
}

.dp__today {
  border: 1px solid var(--dp-primary-color)
}

.dp__week_num {
  color: var(--dp-secondary-color);
  text-align: center
}

.dp__cell_auto_range {
  border-radius: 0;
  border-top: 1px dashed var(--dp-primary-color);
  border-bottom: 1px dashed var(--dp-primary-color)
}

.dp__cell_auto_range_start {
  border-left: 1px dashed var(--dp-primary-color)
}

.dp__cell_auto_range_end {
  border-right: 1px dashed var(--dp-primary-color)
}

.dp__calendar_header_separator {
  width: 100%;
  height: 1px;
  background: var(--dp-border-color)
}

.dp__calendar_next {
  margin-left: 10px
}

.dp__marker_line, .dp__marker_dot {
  height: 5px;
  background-color: var(--dp-marker-color);
  position: absolute;
  bottom: 0
}

.dp__marker_dot {
  width: 5px;
  border-radius: 50%;
  left: 50%;
  transform: translateX(-50%)
}

.dp__marker_line {
  width: 100%;
  left: 0
}

.dp__marker_tooltip {
  position: absolute;
  border-radius: 4px;
  background-color: var(--dp-tooltip-color);
  padding: 5px;
  border: 1px solid var(--dp-border-color);
  z-index: 99999;
  box-sizing: border-box;
  cursor: default
}

.dp__tooltip_content {
  white-space: nowrap
}

.dp__tooltip_text {
  display: flex;
  align-items: center;
  flex-flow: row nowrap;
  color: var(--dp-text-color)
}

.dp__tooltip_mark {
  height: 5px;
  width: 5px;
  border-radius: 50%;
  background-color: var(--dp-text-color);
  color: var(--dp-text-color);
  margin-right: 5px
}

.dp__arrow_bottom_tp {
  left: 50%;
  bottom: 0;
  height: 8px;
  width: 8px;
  background-color: var(--dp-tooltip-color);
  position: absolute;
  border-right: 1px solid var(--dp-border-color);
  border-bottom: 1px solid var(--dp-border-color);
  transform: translate(-50%, 50%) rotate(45deg)
}

.dp__instance_calendar {
  position: relative
}

@media only screen and (max-width: 600px) {
  .dp__flex_display {
    flex-direction: column
  }
}

.dp__cell_highlight {
  background-color: var(--dp-highlight-color)
}

.dp__month_year_row {
  display: flex;
  align-items: center;
  height: 35px;
  color: var(--dp-text-color);
  box-sizing: border-box
}

.dp__inner_nav {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  height: 25px;
  width: 25px;
  color: var(--dp-icon-color);
  text-align: center;
  border-radius: 50%
}

.dp__inner_nav svg {
  height: 20px;
  width: 20px
}

.dp__inner_nav:hover {
  background: var(--dp-hover-color);
  color: var(--dp-hover-icon-color)
}

.dp__inner_nav_disabled:hover, .dp__inner_nav_disabled {
  background: var(--dp-disabled-color);
  color: var(--dp-disabled-color-text);
  cursor: not-allowed
}

.dp__month_year_select {
  width: 50%;
  text-align: center;
  cursor: pointer;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  box-sizing: border-box
}

.dp__month_year_select:hover {
  background: var(--dp-hover-color);
  color: var(--dp-hover-text-color)
}

.dp__month_year_wrap {
  display: flex;
  width: 100%
}

.dp__overlay {
  position: absolute;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  background: var(--dp-background-color);
  top: 0;
  left: 0;
  transition: opacity 1s ease-out;
  z-index: 99999;
  color: var(--dp-text-color);
  box-sizing: border-box
}

.dp__overlay::-webkit-scrollbar-track {
  box-shadow: var(--dp-scroll-bar-background);
  background-color: var(--dp-scroll-bar-background)
}

.dp__overlay::-webkit-scrollbar {
  width: 5px;
  background-color: var(--dp-scroll-bar-background)
}

.dp__overlay::-webkit-scrollbar-thumb {
  background-color: var(--dp-scroll-bar-color);
  border-radius: 10px
}

.dp__overlay:focus {
  border: none;
  outline: none
}

.dp__container_flex {
  display: flex
}

.dp__container_block {
  display: block
}

.dp__overlay_container {
  height: 100%;
  flex-direction: column
}

.dp__overlay_row {
  padding: 0;
  box-sizing: border-box;
  display: flex;
  margin-left: auto;
  margin-right: auto;
  flex-wrap: wrap;
  max-width: 100%;
  width: 100%;
  align-items: center
}

.dp__overlay_container > .dp__overlay_row {
  flex: 1
}

.dp__overlay_col {
  box-sizing: border-box;
  width: 33%;
  padding: 3px;
  white-space: nowrap
}

.dp__overlay_cell_pad {
  padding: 10px 0
}

.dp__overlay_cell_active {
  cursor: pointer;
  border-radius: 4px;
  text-align: center;
  background: var(--dp-primary-color);
  color: var(--dp-primary-text-color)
}

.dp__overlay_cell {
  cursor: pointer;
  border-radius: 4px;
  text-align: center
}

.dp__overlay_cell:hover {
  background: var(--dp-hover-color);
  color: var(--dp-hover-text-color)
}

.dp__cell_in_between {
  background: var(--dp-hover-color);
  color: var(--dp-hover-text-color)
}

.dp__overlay_action {
  position: sticky;
  bottom: 0;
  background: #fff
}

.dp__over_action_scroll {
  right: 5px;
  box-sizing: border-box
}

.dp__overlay_cell_disabled {
  cursor: not-allowed;
  background: var(--dp-disabled-color)
}

.dp__overlay_cell_disabled:hover {
  background: var(--dp-disabled-color)
}

.dp__overlay_cell_active_disabled {
  cursor: not-allowed;
  background: var(--dp-primary-disabled-color)
}

.dp__overlay_cell_active_disabled:hover {
  background: var(--dp-primary-disabled-color)
}

.dp__month_picker_header {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  height: 35px
}

.dp__time_input {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  color: var(--dp-text-color)
}

.dp__time_col_reg {
  padding: 0 20px
}

.dp__time_col_reg_with_button {
  padding: 0 15px
}

.dp__time_col_sec {
  padding: 0 10px
}

.dp__time_col_sec_with_button {
  padding: 0 5px
}

.dp__time_col {
  font-size: 2rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
}

.dp__time_display {
  cursor: pointer;
  color: var(--dp-text-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 3px
}

.dp__time_display:hover {
  background: var(--dp-hover-color);
  color: var(--dp-hover-text-color)
}

.dp__inc_dec_button {
  padding: 5px;
  margin: 0;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  color: var(--dp-icon-color);
  box-sizing: border-box
}

.dp__inc_dec_button svg {
  height: 32px;
  width: 32px
}

.dp__inc_dec_button:hover {
  background: var(--dp-hover-color);
  color: var(--dp-primary-color)
}

.dp__pm_am_button {
  background: var(--dp-primary-color);
  color: var(--dp-primary-text-color);
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer
}

.dp__action_row {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  color: var(--dp-text-color);
  background: var(--dp-background-color)
}

.dp__action_row svg {
  height: 20px;
  width: auto
}

.dp__selection_preview {
  width: 50%;
  color: var(--dp-text-color);
  font-size: .8rem
}

.dp__action_buttons {
  width: 50%;
  text-align: right
}

.dp__action {
  font-weight: bold;
  cursor: pointer;
  padding: 2px 5px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center
}

.dp__select {
  color: var(--dp-success-color)
}

.dp__action_disabled {
  color: var(--dp-success-color-disabled);
  cursor: not-allowed
}

.dp__cancel {
  color: var(--dp-secondary-color)
}

.dp__theme_dark {
  --dp-background-color: #212121;
  --dp-text-color: #fff;
  --dp-hover-color: #484848;
  --dp-hover-text-color: #fff;
  --dp-hover-icon-color: #959595;
  --dp-primary-color: #005cb2;
  --dp-primary-disabled-color: #61a8ea;
  --dp-primary-text-color: #fff;
  --dp-secondary-color: #a9a9a9;
  --dp-border-color: #2d2d2d;
  --dp-menu-border-color: #2d2d2d;
  --dp-border-color-hover: #aaaeb7;
  --dp-disabled-color: #737373;
  --dp-disabled-color-text: #d0d0d0;
  --dp-scroll-bar-background: #212121;
  --dp-scroll-bar-color: #484848;
  --dp-success-color: #00701a;
  --dp-success-color-disabled: #428f59;
  --dp-icon-color: #959595;
  --dp-danger-color: #e53935;
  --dp-marker-color: #e53935;
  --dp-tooltip-color: #3e3e3e;
  --dp-highlight-color: rgb(0 92 178 / 20%)
}

.dp__theme_light {
  --dp-background-color: #fff;
  --dp-text-color: #212121;
  --dp-hover-color: #f3f3f3;
  --dp-hover-text-color: #212121;
  --dp-hover-icon-color: #959595;
  --dp-primary-color: #1976d2;
  --dp-primary-disabled-color: #6bacea;
  --dp-primary-text-color: #f8f5f5;
  --dp-secondary-color: #c0c4cc;
  --dp-border-color: #ddd;
  --dp-menu-border-color: #ddd;
  --dp-border-color-hover: #aaaeb7;
  --dp-disabled-color: #f6f6f6;
  --dp-scroll-bar-background: #f3f3f3;
  --dp-scroll-bar-color: #959595;
  --dp-success-color: #76d275;
  --dp-success-color-disabled: #a3d9b1;
  --dp-icon-color: #959595;
  --dp-danger-color: #ff6f60;
  --dp-marker-color: #ff6f60;
  --dp-tooltip-color: #fafafa;
  --dp-disabled-color-text: #8e8e8e;
  --dp-highlight-color: rgb(25 118 210 / 10%)
}

.dp__main {
  user-select: none;
  box-sizing: border-box
}

.dp__pointer {
  cursor: pointer
}

.dp__icon {
  stroke: currentcolor;
  fill: currentcolor
}

.dp__button {
  width: 100%;
  text-align: center;
  color: var(--dp-icon-color);
  background: var(--dp-background-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  padding: 10px;
  box-sizing: border-box;
  height: 35px
}

.dp__button:hover {
  background: var(--dp-hover-color);
  color: var(--dp-hover-icon-color)
}

.dp__button svg {
  height: 20px;
  width: auto
}

.dp__button_bottom {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px
}

.dp__flex_display {
  display: flex
}

.dp__flex_display_with_input {
  flex-direction: column;
  align-items: start
}

.dp__relative {
  position: relative
}

.calendar-next-enter-active, .calendar-next-leave-active, .calendar-prev-enter-active, .calendar-prev-leave-active {
  transition: all .1s ease-out
}

.calendar-next-enter-from {
  opacity: 0;
  transform: translateX(22px)
}

.calendar-next-leave-to {
  opacity: 0;
  transform: translateX(-22px)
}

.calendar-prev-enter-from {
  opacity: 0;
  transform: translateX(-22px)
}

.calendar-prev-leave-to {
  opacity: 0;
  transform: translateX(22px)
}

.dp-menu-appear-enter-active, .dp-menu-appear-leave-active, .dp-slide-up-enter-active, .dp-slide-up-leave-active, .dp-slide-down-enter-active, .dp-slide-down-leave-active {
  transition: all .1s ease-out
}

.dp-slide-down-leave-to, .dp-slide-up-enter-from {
  opacity: 0;
  transform: translateY(22px)
}

.dp-slide-down-enter-from, .dp-slide-up-leave-to {
  opacity: 0;
  transform: translateY(-22px)
}

.dp-menu-appear-enter-from {
  opacity: 0
}

.dp-menu-appear-enter-active, .dp-menu-appear-leave-active {
  transition: opacity .1s ease
}

.dp-menu-appear-leave-to {
  opacity: 1
}
