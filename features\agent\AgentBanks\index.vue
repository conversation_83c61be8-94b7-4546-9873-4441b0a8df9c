<template>
  <div>
    <PageHeader :title="routes.agentBanks.name" />
    <div class="flex justify-end">
      <Modal
        v-model="isShowCreateModal"
        class="modal-md"
        title="สร้าง บัญชีธนาคาร"
        :no-backdrop-close="true"
      >
        <CreateForm :status="bank.addStatus" @submit="bank.add" />
      </Modal>
      <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">
        เพิ่มบัญชีธนาคาร
      </Button>
    </div>
    <Table :options="tableOptions" @pageChange="bank.fetch" @search="bank.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { useAgentBankPageLoader } from '~/loaders/agentBank'
import CreateForm from '~/features/agent/AgentBanks/CreateForm.vue'
import { IBankItem } from '~/models/bank'
import { COLUMN_TYPES } from '~/components/Table/types'
import Switcher from '~/features/agent/AgentBanks/Switcher.vue'
import ColumnAction from '~/features/agent/AgentBanks/ColumnAction.vue'
import {
  useAgentBankToggleLimitObjectLoader,
  useAgentBankToggleShowOnWebObjectLoader,
  useAgentBankToggleToggleMobileApplicationObjectLoader,
  useAgentBankToggleWithdrawAutoObjectLoader,
} from '~/loaders/agentBankToggle'

const bank = useAgentBankPageLoader()
const toggleShowOnWeb = useAgentBankToggleShowOnWebObjectLoader()
const toggleWithdrawAuto = useAgentBankToggleWithdrawAutoObjectLoader()
const toggleMobileApplication = useAgentBankToggleToggleMobileApplicationObjectLoader()
const toggleLimit = useAgentBankToggleLimitObjectLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)

bank.setFetchLoading()
onMounted(() => {
  bank.fetch()
})

useWatchTrue(
  () => bank.addStatus.isSuccess,
  () => {
    isShowCreateModal.value = false

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างแบงค์สำเร็จ',
      })
      .then(() => {
        bank.fetch()
      })
  }
)

useWatchTrue(
  () => bank.addStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(bank.addStatus.errorData),
    })
  }
)

const tableOptions = useTable<IBankItem>({
  repo: bank,
  columns: () => [
    {
      value: 'บัญชี',
    },
    {
      value: 'ค่าธรรมเนียม',
    },
    {
      value: 'คงเหลือ',
    },
    {
      value: 'เปิดใช้งาน',
    },
    // {
    //   value: 'เปิดใช้งานลิมิต',
    // },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: {
            image_url: item.bank.image_url,
            name: item.account_name,
            number: item.account_number,
            bank: item.bank.name_th,
          },
          type: COLUMN_TYPES.BANK,
        },
        {
          type: COLUMN_TYPES.NUMBER,
          value: item.fee,
        },
        {
          type: COLUMN_TYPES.NUMBER,
          value: item.balance,
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          props: {
            action: async (enabled: boolean) => {
              await toggleShowOnWeb.run({ id: item.id, enabled })
            },
            value: item.enabled_show_on_web,
          },
          value: Switcher,
        },
        // {
        //   type: COLUMN_TYPES.COMPONENT,
        //   props: {
        //     action: async (enabled: boolean) => {
        //       await toggleLimit.run({ id: item.id, enabled })
        //     },
        //     value: item.enabled_limit,
        //   },
        //   value: Switcher,
        // },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            item,
          },
        },
      ]
    }),
})
</script>
