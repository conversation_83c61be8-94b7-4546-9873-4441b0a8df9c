<template>
  <div>
    <Loader :is-loading="promotionAllLoader.status.isLoading">
      <draggable
        v-model="promoList"
        item-key="id"
        class="grid grid-cols-1 gap-4"
        @change="handleOnSave"
      >
        <template #item="{ element }">
          <div class="bg-admin p-4 rounded-lg shadow grid grid-cols-3 gap-4 cursor-pointer">
            <div class="w-full h-24 rounded overflow-hidden">
              <img
                v-if="element.image_url_th || element.image_url_en"
                class="w-full h-full object-contain"
                :src="element.image_url_th || element.image_url_en"
                :alt="element.name_th || element.name_en"
              />
            </div>
            <div class="col-span-2 grid grid-cols-3 gap-x-2">
              <div class="col-span-1 font-semibold">ชื่อโปรโมชั่น</div>
              <div class="col-span-2">{{ element.name_th || element.name_en }}</div>
              <div class="col-span-1 font-semibold">ประเภท</div>
              <div class="col-span-2">
                {{ PROMOTION_TYPE_DISPLAY[element.promotion_type] }}
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </Loader>
  </div>
</template>

<script lang="tsx" setup>
import draggable from 'vuedraggable'
import { useAgentPromotionAllLoader } from '~/loaders/agentPromotion'
import { IPromotionItem } from '~/models/promotion'
import { PROMOTION_TYPE_DISPLAY, PromotionType } from '~/constants/promotion'

const emits = defineEmits(['sorted'])

const dialog = useDialog()
const promotionAllLoader = useAgentPromotionAllLoader()

const promoList = ref<IPromotionItem[]>([])

onMounted(() => {
  promotionAllLoader.run()
})

const handleOnSave = () => {
  const result = promoList.value.map((item) => item.id)

  emits('sorted', result)
}

useWatchTrue(
  () => promotionAllLoader.status.isSuccess,
  () => {
    promoList.value = promotionAllLoader.items
  }
)

useWatchTrue(
  () => promotionAllLoader.status.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(promotionAllLoader.status.errorData),
    })
  }
)
</script>
