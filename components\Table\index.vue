<template>
  <div :class="['table-component']">
    <div v-if="!options.isHideToolbar" class="flex items-center justify-between mb-4">
      <input
        v-model="searchText"
        type="search"
        class="form-control w-full md:w-[400px]"
        placeholder="ค้นหา..."
      />
      <div>
        <slot name="toolbar" />
      </div>
    </div>

    <div :class="['table-wrapper', tableWrapperClassName]">
      <table :class="['table', tableClassName]">
        <TableHead
          :class-name="tableHeadClassName"
          :columns="options.columns"
          :is-show-checkbox="options.isShowCheckbox"
          :rows-index="rowsIndex"
          :rows-index-select="rowsIndexSelect"
          :status="options.status"
        />
        <TableBody
          :disabled-check-indexes="options.disabledCheckIndexes"
          :is-show-checkbox="options.isShowCheckbox"
          :is-row-clickable="options.isRowClickable"
          :on-row-click="options.onRowClick"
          :primary="options.primary"
          :raw-data="options.rawData"
          :rows="options.rows"
          :rows-index-select="rowsIndexSelect"
          :status="options.status"
        />
      </table>
      <TableStatus :rows="props.options.rows" :status="options.status" />
    </div>
    <div
      v-if="!options.isHideBottomPagination && options.pageOptions.totalCount"
      class="table-bottom-pagination"
    >
      <div class="flex items-center">
        <p class="whitespace-nowrap mr-3">
          ผลลัพธ์ {{ pageBetween }} ของ {{ totalCountWithComma }} รายการ
        </p>
        <Listbox
          :model-value="props.options.limit"
          @update:model-value="props.options.setLimit($event)"
        >
          <div :class="['form-control select-box min-w-[80px] !min-h-[10px] !py-1']">
            <ListboxButton :class="['select-listbox-button']">
              <span class="select-label">{{ props.options.limit }}</span>
              <span class="select-icons-box">
                <i aria-hidden="true" class="ic ic-chevron-down-solid select-icons-down" />
              </span>
            </ListboxButton>
            <transition
              leave-active-class="transition duration-100 ease-in"
              leave-from-class="opacity-100"
              leave-to-class="opacity-0"
            >
              <ListboxOptions class="select-listbox-options mt-4 bottom-12">
                <div class="my-2">
                  <ListboxOption
                    v-for="option in pageLimitOptions"
                    :key="option.value"
                    v-slot="{ selected, active }"
                    :value="option.value"
                    as="template"
                  >
                    <li
                      :class="[
                        'select-options',
                        { 'bg-primary-200 text-dark': active || selected, 'text-dark': !active },
                      ]"
                    >
                      <span class="select-label">
                        {{ option.label }}
                      </span>
                    </li>
                  </ListboxOption>
                </div>
              </ListboxOptions>
            </transition>
          </div>
        </Listbox>
      </div>

      <Pagination
        :current-page="options.pageOptions.currentPage"
        :page-count="options.pageOptions.totalPage"
        @pageChange="handlePageChange"
      />
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { PropType } from 'vue'
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/vue'
import Pagination from '../Pagination.vue'
import { ITableOptions } from './types'
import TableHead from './TableHead.vue'
import TableBody from './TableBody.vue'
import TableStatus from './TableStatus.vue'
import { StringHelper } from '~/utils/StringHelper'
import { useWatchTrue } from '~/hooks/useWatch'

const props = defineProps({
  tableWrapperClassName: { type: [String, Object] },
  tableClassName: { type: [String, Object] },
  tableHeadClassName: { type: [String, Object] },
  options: { type: Object as PropType<ITableOptions>, required: true },
})

const emit = defineEmits<{
  (event: 'pageChange', value: number): void
  (event: 'search', value: string): void
}>()

const router = useRouter()
const searchText = ref('')

const pageLimitOptions = [
  { label: '10', value: 10 },
  { label: '20', value: 20 },
  { label: '30', value: 30 },
  { label: '50', value: 50 },
  { label: '100', value: 100 },
]

watch(
  () => searchText.value,
  _debounce((value) => {
    emit('search', value)
  }, 500)
)

// Rows Select Checkbox
const rowsIndex = ref<number[]>([])
const rowsIndexSelect = ref<number[]>([])

useWatchTrue(
  () => props.options.status.isSuccess,
  () => {
    clearRowIndex()
    rowsIndex.value = props.options.rows
      .map((_, index) => index)
      .filter((index) => {
        const disabledList = props.options.disabledCheckIndexes ?? []

        return !disabledList.includes(index)
      })
  }
)

watch(rowsIndexSelect.value, (value) => {
  if (typeof props.options.onCheckBoxClick === 'function') {
    props.options.onCheckBoxClick(value)
  }
})

// Pagination
const pageBetween = computed((): string => {
  const length = props.options.rows.length

  if (length === 0) {
    return '0'
  }

  const start = (props.options.pageOptions.currentPage - 1) * props.options.pageOptions.limit + 1

  return `${start} - ${start + props.options.pageOptions.currentPageCount - 1}`
})

const totalCountWithComma = computed((): string => {
  return !props.options.pageOptions.totalCount
    ? '0'
    : StringHelper.withComma(props.options.pageOptions.totalCount)
})

const handlePageChange = (page: number) => {
  emit('pageChange', page)

  if (!props.options.isNotChangeRoute) {
    router.push({ query: { page } })
  }
}

const clearRowIndex = () => {
  rowsIndexSelect.value.splice(0, rowsIndexSelect.value.length)
}

watch(
  () => props.options.limit,
  () => {
    emit('pageChange', 1)
  }
)
</script>
