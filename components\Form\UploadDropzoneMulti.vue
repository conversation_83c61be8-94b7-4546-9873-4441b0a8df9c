<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      class="dropzone"
      :class="[{ 'bg-gray-fill': isDragover }, dropzoneClassName, fieldClassName]"
      @dragover="dragover"
      @dragleave="dragleave"
      @drop="drop"
    >
      <input
        ref="fileInput"
        type="file"
        multiple
        :accept="acceptFile"
        :disabled="disabled"
        @change="handleChange"
      />
      <div class="dropzone-wrapper">
        <div v-if="selectedFiles && selectedFiles.length > 0" class="dropzone-multi-preview">
          <div
            v-for="(file, index) in selectedFiles"
            :key="`file-${index}`"
            class="dropzone-multi-item"
          >
            <div v-if="isImage(file)" class="preview-img">
              <img :src="generateURL(file)" alt="file" />
            </div>
            <div class="preview-file-name">
              <p class="truncate">{{ file.name }}</p>
              <em class="ic ic-x-mark-solid close-icon" @click="handleDeleteFile(index)" />
            </div>
            <div class="preview-file-info">
              File Size - {{ convertFileSize(file.size) + ' MB' }}
            </div>
          </div>
        </div>
        <Progressbar v-if="upload.status.value.isLoading" class="w-3/4 mb-8" :percent="percent" />
        <Button class="btn-primary" @click="handleOpenFile"> เลือกไฟล์ </Button>
        <div class="dropzone-message">
          <p>หรือลากวางไฟล์ของคุณในกล่องนี้</p>
          <p v-if="acceptFileSize">ขนาดไม่เกิน: {{ (acceptFileSize / 1000).toFixed(2) }} MB</p>
        </div>
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="tsx" setup>
import { IUploadDropzoneProps } from './upload_dropzone.types'
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { useUploadLoader } from '~/loaders/useUploadLoader'

const props = withDefaults(defineProps<IUploadDropzoneProps>(), {})
const emit = defineEmits(['change', 'delete'])

const {
  wrapperProps,
  fieldClassName,
  disabled,
  handleChange: onChange,
  setErrors,
} = useFieldHOC<string>(props)

const upload = useUploadLoader()
const fileInput = ref<HTMLInputElement>()
const selectedFiles = ref<File[] | undefined>([])
const isDragging = ref<boolean>(false)
const isDragover = ref<boolean>(false)
const percent = ref<number>(0)

const acceptFile = computed(() =>
  typeof props.accept === 'string' ? props.accept : props.accept?.join(',')
)

const acceptFileSize = computed(() => props.maxSize)

const handleChange = async (e: Event) => {
  const files = (e.target as HTMLInputElement).files
  const result = handleCheckFileCondition(files)
  let urls: string[] = []

  if (result) {
    for (let i = 0; i < result.length; i++) {
      const formData = new FormData()

      formData.append('file', result[i] as any)

      await upload.run(formData, { data: { onUploadProgress, onDownloadProgress } })
      urls = [...urls, upload.data.value!.url]
      percent.value = 0
    }

    selectedFiles.value?.push(...result)
    onChange(urls)
    emit('change', urls)
  }
}

const handleOpenFile = () => {
  fileInput.value?.click()
}

const handleDeleteFile = (index: number) => {
  selectedFiles.value?.splice(index, 1)
  emit('delete', index)
  emit('change', selectedFiles.value)
}

const handleCheckFileCondition = (files: FileList | null | undefined): File[] | undefined => {
  if (files && files.length > 0) {
    const errorsMessage: string[] = []
    const result: File[] = []

    for (const file of files) {
      const accept = checkAcceptFile(file)

      if (!accept) {
        errorsMessage.push(`${file.name} - File type is not supported`)
        continue // skip to next file
      }

      const maxSize = checkMaxSize(file)

      if (!maxSize) {
        errorsMessage.push(`${file.name} - File size is too large`)
        continue // skip to next file
      }

      result.push(file)
    }

    setErrors(errorsMessage)

    return result
  }
}

const dragover = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
  isDragover.value = true
}

const dragleave = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  isDragover.value = false
}

const drop = (e: DragEvent) => {
  e.preventDefault()
  const files = e.dataTransfer?.files
  const result = handleCheckFileCondition(files)

  isDragging.value = false
  isDragover.value = false

  if (result) {
    selectedFiles.value?.push(...result)
    emit('change', selectedFiles.value)
  }
}

const isImage = (file: File) => {
  return file.type.startsWith('image/')
}

const checkAcceptFile = (file: File): boolean => {
  const fileType = file.type.split('/')[1]

  return acceptFile.value ? acceptFile.value.includes(fileType) : true
}

const checkMaxSize = (file: File): boolean => {
  if (acceptFileSize.value) {
    return file.size / 1000 <= acceptFileSize.value
  }

  return true
}

const convertFileSize = (size: number) => {
  return (size / 1000 / 1000).toFixed(2)
}

const generateURL = (file: File) => {
  const fileSrc = URL.createObjectURL(file)

  setTimeout(() => {
    URL.revokeObjectURL(fileSrc)
  }, 1000)

  return fileSrc
}

const onUploadProgress = (progressEvent: ProgressEvent) => {
  percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.8
}

const onDownloadProgress = (progressEvent: ProgressEvent) => {
  if (progressEvent.total === 0) {
    percent.value = 100

    return
  }

  percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.2 + 80
}
</script>
