<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentDeposits />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentDeposits from '~/features/agent/AgentDeposits/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentDeposits.name })
app.updatePageMeta({
  title: routes.agentDeposits.name,
  breadcrumbs: [routes.agentDeposits],
})
</script>
