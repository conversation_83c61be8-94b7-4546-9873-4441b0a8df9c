<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentUsers />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentUsers from '~/features/agent/AgentUsers/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentUsers.name })
app.updatePageMeta({
  title: routes.agentUsers.name,
  breadcrumbs: [routes.agentUsers],
})
</script>
