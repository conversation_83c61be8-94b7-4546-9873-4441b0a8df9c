<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      {{ item ? 'แก้ไข' : 'สร้าง' }}
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IAnnounceItem } from '~/models/article'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item?: IAnnounceItem
}>()

const form = useForm<Partial<IAnnounceItem>>({
  initialValues: props.item || {
    started_at: new Date().toISOString(),
    is_enabled: true,
    slug: StringHelper.genString(6),
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'title',
      label: 'หัวข้อ',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'slug',
      label: 'Slug',
      isDisabled: ParamHelper.getBoolFalse(props.item),
      rules: yup.string().min(3).required(),
    },
  },
  {
    type: INPUT_TYPES.WYSIWYG,
    props: {
      name: 'description',
      label: 'รายละเอียด',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      name: 'image_url',
      label: 'รูปภาพ',
      rules: yup.string().url().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'link',
      label: 'ลิงก์',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.DATE_TIME,
    props: {
      name: 'started_at',
      label: 'วันเริ่ม',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.DATE_TIME,
    props: {
      name: 'ended_at',
      label: 'วันสิ้นสุด',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      name: 'is_enabled',
      label: 'เปิดใช้งาน',
      rules: yup.boolean(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    meta: { ...data.meta },
  })
})
</script>
