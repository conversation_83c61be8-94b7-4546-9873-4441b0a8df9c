<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentBanks />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentBanks from '~/features/agent/AgentBanks/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentBanks.name })
app.updatePageMeta({
  title: routes.agentBanks.name,
  breadcrumbs: [routes.agentBanks],
})
</script>
