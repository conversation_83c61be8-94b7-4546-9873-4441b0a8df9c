<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { useAgentBankPageLoader } from '~/loaders/agentBank'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const bank = useBankListLoader()
const bankMaster = useAgentBankPageLoader()
const promotion = useAgentPromotionPageLoader()
const form = useForm<
  Partial<{
    tel: string
    agent_bank_account_id: number
    amount: number
    promotion: string
    promotion_id: string
  }>
>({
  initialValues: {
    promotion: 'auto',
  },
})

bank.setLoading()
bankMaster.setFetchLoading()
promotion.setFetchLoading()

onMounted(() => {
  bank.run()
  bankMaster.fetch(1, '', {
    params: {
      limit: 1000,
    },
  })

  promotion.fetch(1, '', {
    params: {
      limit: 1000,
    },
  })
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      name: 'proof_image_url',
      placeholder: 'หลักฐานการโอน',
      label: 'หลักฐานการโอน',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'tel',
      placeholder: 'เบอร์โทรศัพท์',
      label: 'เบอร์โทรศัพท์',
      rules: yup.string().numeric().thaiPhone().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      class: 'mb-3',
      name: 'agent_bank_account_id',
      placeholder: 'ธนาคาร',
      label: 'ธนาคาร',
      rules: yup.string().required(),
      isLoading: bankMaster.fetchStatus.isLoading,
      options: bankMaster.fetchItems.map((item) => ({
        label: item.bank.name_th + ' ' + item.account_name + ' ' + item.account_number,
        value: item.id,
      })),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      class: 'mb-3',
      name: 'amount',
      placeholder: 'จำนวนเงิน',
      label: 'จำนวนเงิน',
      rules: yup.number().min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      class: 'mb-3',
      name: 'promotion',
      placeholder: 'ประเภทโปรโมชั่น',
      label: 'ประเภทโปรโมชั่น',
      rules: yup.string().required(),
      options: [
        {
          label: 'auto',
          value: 'auto',
        },
        {
          label: 'off',
          value: 'off',
        },
        {
          label: 'manual',
          value: 'manual',
        },
      ],
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    isHide: form.values?.promotion === 'auto',
    props: {
      class: 'mb-3',
      name: 'promotion_id',
      placeholder: 'โปรโมชั่น',
      label: 'โปรโมชั่น',
      rules: yup.string().required(),
      options: promotion.fetchItems.map((item) => ({
        label: item.name,
        value: item.id,
      })),
    },
  },
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      name: 'remark',
      placeholder: 'remark',
      label: 'remark',
      rules: yup.string(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    amount: Number(data.amount),
  })
})
</script>
