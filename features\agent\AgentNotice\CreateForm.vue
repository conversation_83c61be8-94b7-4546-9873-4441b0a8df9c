<template>
  <Form @submit="onSubmit">
    <FormFields class="grid grid-cols-6 gap-x-2 items-center" :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const form = useForm<
  Partial<{
    type: string
    tag: string
    image_url: string
    image_urls: string[]
  }>
>()

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    class: 'col-span-full',
    props: {
      name: 'type',
      placeholder: 'ประเภท',
      options: ArrayHelper.toOptions([
        { id: 'static', name: 'รูปภาพ' },
        { id: 'slide', name: 'สไลด์' },
        { id: 'popup', name: 'กล่องข้อความ' },
      ]),
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    class: 'col-span-full',
    props: {
      name: 'tag',
      placeholder: 'แท็ก (ห้ามซ้ำ)',
      rules: yup.string().required(),
    },
  },
  ...((): any[] => {
    if (form.values.type === 'slide') {
      return [
        {
          type: INPUT_TYPES.UPLOAD_DROPZONE_MULTI,
          class: 'col-span-full',
          props: {
            label: 'อัพโหลดรูปภาพ(อัพโหลดได้หลายรูป)',
            name: 'image_urls',
            rules: yup.array().required(),
          },
        },
      ]
    }

    return [
      {
        type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
        class: 'col-span-full',
        props: {
          label: 'อัพโหลดรูปภาพ',
          name: 'image_url',
          rules: yup.string().required(),
        },
      },
    ]
  })(),
])

const onSubmit = form.handleSubmit((data) => {
  if (data.type !== 'slide') {
    data.image_urls = [data.image_url!]
  }

  delete data.image_url
  emits('submit', data)
})
</script>
