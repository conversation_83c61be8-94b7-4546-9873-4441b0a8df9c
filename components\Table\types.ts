import { type IPageOptions } from '~~/lib/api/types'

export const enum COLUMN_TYPES {
  VALUE = 'VALUE',
  COMPONENT = 'COMPONENT',
  DATE_TIME = 'DATE_TIME',
  NUMBER = 'NUMBER',
  ACTION = 'ACTION',
  BOOLEAN = 'BOOLEAN',
  IMAGE = 'IMAGE',
  LINK = 'LINK',
  BANK = 'BANK',
  STATUS = 'STATUS',
  PLAYER = 'PLAYER',
}

export interface IColumn {
  value: string | any
  title?: string
  className?: string
  props?: object
  enabledSort?: boolean
  isComponent?: boolean
}

export interface IRowItem<T = Record<string, any>, O = Record<string, any>> {
  value: string | any
  type?: COLUMN_TYPES
  title?: string
  className?: string
  isSelect?: boolean
  props?: T
  on?: O
}

export interface IRow<T = object> {
  [index: number]: IRowItem<T>
}

export interface IColumnProps<P> {
  item: IRowItem<P>
  data: Record<string, any>
}

export interface IStatus {
  isError: boolean
  isSuccess: boolean
  isLoading: boolean
  isLoaded: boolean
  errorData: any | null
}

export interface ITableOptions<T = object> {
  rawData: T[]
  primary: string
  isHideBottomPagination?: boolean
  isHideTopPagination?: boolean
  isNotChangeRoute: boolean
  status: IStatus
  pageOptions: IPageOptions
  columns: IColumn[]
  rows: Array<IRow<T>>
  isHideToolbar?: boolean
  isShowCheckbox?: boolean
  isRowClickable?: boolean
  deleteStatus?: IStatus
  onRowClick?: (index: number) => void
  onCheckBoxClick?: (index: number[]) => void
  disabledCheckIndexes?: number[]
  limit: number
  setLimit: (limit: number) => void
}

export interface ISimpleTableOptions<T = object> {
  rawData: T[]
  primary: string
  status: IStatus
  columns: IColumn[]
  rows: Array<IRow<T>>
  isShowCheckbox?: boolean
  isHideBottomPagination?: boolean
  onRowClick?: (index: number, columns: Array<{ value: string }>) => void
  onCheckBoxClick?: (index: number[]) => void
}
