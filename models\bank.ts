export interface IMasterBankItem {
  id: string
  name_th: string
  name_en: string
  code: string
  swift_code: string
  image_url: string
  slug: string
}

export interface IBankItem {
  id: string
  agent_id: string
  bank_id: string
  account_name: string
  account_number: string
  allowed_bank: string[]
  type: string
  min_balance: number
  balance: number
  fee: number

  enabled_withdraw_otp: boolean
  enabled_show_on_web: boolean
  enabled_limit: boolean
  enabled_mobile_application: boolean
  enabled_withdraw_auto: boolean

  is_default: boolean
  is_in_group: boolean
  bank: IMasterBankItem

  scb_bank_device_id: string
  scb_bank_device?: {
    id: string
    agent_bank_account_id: string
    is_enabled: string
    created_at: string
    updated_at: string
  }
}
