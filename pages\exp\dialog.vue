<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Dialog" />
    <div class="my-3 mx-auto bg-white p-8 rounded-10">
      <h3 class="topic-heading mb-4">Dialog (default)</h3>
      <Button :class="'btn-success mr-4'" @click="onSet('success')"> Success </Button>
      <Button :class="'btn-danger mr-4'" @click="onSet('error')"> Error </Button>
      <Button :class="'btn-info mr-4'" @click="onSet('info')"> Info </Button>
      <Button :class="'btn-warning mr-4'" @click="onSet('warning')"> Warning </Button>
    </div>

    <div class="my-3 mx-auto bg-white p-8 rounded-10">
      <h3 class="topic-heading mb-4">Dialog (with <PERSON><PERSON> Button)</h3>
      <Button :class="'btn-success-outline mr-4'" @click="onSetShowCancel('success')">
        Success
      </Button>
      <Button :class="'btn-danger-outline mr-4'" @click="onSetShowCancel('error')"> Error </Button>
      <Button :class="'btn-info-outline mr-4'" @click="onSetShowCancel('info')"> Info </Button>
      <Button :class="'btn-warning-outline mr-4'" @click="onSetShowCancel('warning')">
        Warning
      </Button>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { DialogType, useDialog } from '~/hooks/useDialog'
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'

import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.dialog.name })
app.updatePageMeta({
  title: styleguideMenu.dialog.name,
  breadcrumbs: [styleguideMenu.dialog],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))
const dialog = useDialog()

const onSetShowCancel = (type: string) => {
  if (type === DialogType.SUCCESS) {
    dialog.success({
      title: 'success',
      message: 'success',
      isShowCancelBtn: true,
    })
  }

  if (type === DialogType.ERROR) {
    dialog.error({
      title: 'error',
      message: 'error',
      isShowCancelBtn: true,
    })
  }

  if (type === DialogType.INFO) {
    dialog.info({
      title: 'info',
      message: 'info',
      isShowCancelBtn: true,
    })
  }

  if (type === DialogType.WARNING) {
    dialog.warning({
      title: 'warning',
      message: 'warning',
      isShowCancelBtn: true,
    })
  }
}

const onSet = (type: string) => {
  if (type === DialogType.SUCCESS) {
    dialog.success({
      title: 'success',
      message: 'success',
    })
  }

  if (type === DialogType.ERROR) {
    dialog.error({
      title: 'error',
      message: 'error',
    })
  }

  if (type === DialogType.INFO) {
    dialog.info({
      title: 'info',
      message: 'info',
    })
  }

  if (type === DialogType.WARNING) {
    dialog.warning({
      title: 'warning',
      message: 'warning',
    })
  }
}
</script>
