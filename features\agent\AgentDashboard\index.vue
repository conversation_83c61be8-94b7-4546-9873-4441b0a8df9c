<template>
  <div>
    <FormFields :form="form" :options="fields" class="max-w-[300px]" />
    <Loader :is-loading="stat.status.value.isLoading">
      <div class="space-y-12">
        <div>
          <PageHeader
            :title="`สรุปยอดเดือนนี้ (${TimeHelper.getDateFormTime(form.values.date).slice(0, 7)})`"
          />

          <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            <div
              v-for="(item, index) in monthlyStats"
              :key="index"
              class="relative overflow-hidden rounded-lg bg-white px-4 pb-12 pt-5 shadow sm:px-6 sm:pt-6"
            >
              <dt>
                <div :class="`absolute rounded-md ${item.bgColor} p-3`">
                  <component :is="item.icon" class="h-6 w-6 text-white" />
                </div>
                <p class="ml-16 truncate text-sm font-medium text-gray-500">{{ item.title }}</p>
              </dt>
              <dd class="ml-16 flex items-baseline pb-6 sm:pb-7">
                <p class="text-2xl font-semibold text-gray-900">
                  {{ StringHelper.withComma(stat.data.value?.[item.dataKey]) }}
                </p>
                <div
                  v-if="item.hasDetails"
                  class="absolute inset-x-0 bottom-0 bg-gray-50 px-4 py-4 sm:px-6"
                >
                  <div class="text-sm">
                    <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                      ดูรายละเอียด
                    </a>
                  </div>
                </div>
              </dd>
            </div>
          </dl>
        </div>
        <div>
          <PageHeader :title="`สรุปยอดวันนี้ (${TimeHelper.getDateFormTime(form.values.date)})`" />

          <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            <div
              v-for="(item, index) in dailyStats"
              :key="index"
              class="relative overflow-hidden rounded-lg bg-white px-4 pb-12 pt-5 shadow sm:px-6 sm:pt-6"
            >
              <dt>
                <div :class="`absolute rounded-md ${item.bgColor} p-3`">
                  <component :is="item.icon" class="h-6 w-6 text-white" />
                </div>
                <p class="ml-16 truncate text-sm font-medium text-gray-500">{{ item.title }}</p>
              </dt>
              <dd class="ml-16 flex items-baseline pb-6 sm:pb-7">
                <p class="text-2xl font-semibold text-gray-900">
                  {{ StringHelper.withComma(stat.data.value?.[item.dataKey]) }}
                </p>
                <div
                  v-if="item.hasDetails"
                  class="absolute inset-x-0 bottom-0 bg-gray-50 px-4 py-4 sm:px-6"
                >
                  <div class="text-sm">
                    <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                      ดูรายละเอียด
                    </a>
                  </div>
                </div>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </Loader>
  </div>
</template>

<script lang="ts" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import {
  UserGroupIcon,
  GiftIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  ArrowUturnUpIcon,
  ChartBarIcon,
} from '@heroicons/vue/24/outline'
import { useAgentStatsLoader } from '~/loaders/agentStats'
import { INPUT_TYPES } from '~/components/Form/types'

const stat = useAgentStatsLoader()
const form = useForm({
  initialValues: {
    date: TimeHelper.getCurrentDate(),
  },
})

stat.setLoading()
onMounted(() => {
  stat.run(
    {},
    {
      params: {
        date: TimeHelper.getDateFormTime(form.values.date),
      },
    }
  )
})

const fields = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันที่',
      name: 'date',
      rules: yup.string().required(),
      max_date: TimeHelper.getCurrentDate(),
    },
  },
])

const currentDate = form.useFieldModel('date')

watch(
  () => currentDate.value,
  () => {
    stat.run(
      {},
      {
        params: {
          date: TimeHelper.getDateFormTime(form.values.date),
        },
      }
    )
  }
)

const monthlyStats = [
  {
    title: 'สมาชิกเดือนนี้',
    icon: UserGroupIcon,
    bgColor: 'bg-blue-500',
    dataKey: 'monthly_player',
    hasDetails: true,
  },
  {
    title: 'รับเครดิตฟรีเดือนนี้',
    icon: GiftIcon,
    bgColor: 'bg-green-500',
    dataKey: 'monthly_free_credit',
    hasDetails: false,
  },
  {
    title: 'จำนวนเครดิตฟรีที่เสียเดือนนี้',
    icon: ArrowTrendingDownIcon,
    bgColor: 'bg-red-500',
    dataKey: 'monthly_free_credit_lose',
    hasDetails: false,
  },
  {
    title: 'ยอดฝากรวมเดือนนี้',
    icon: CurrencyDollarIcon,
    bgColor: 'bg-yellow-500',
    dataKey: 'monthly_deposit',
    hasDetails: true,
  },
  {
    title: 'ยอดถอนรวมเดือนนี้',
    icon: ArrowUturnUpIcon,
    bgColor: 'bg-purple-500',
    dataKey: 'monthly_withdraw',
    hasDetails: true,
  },
  {
    title: 'สรุปรายได้เดือนนี้',
    icon: ChartBarIcon,
    bgColor: 'bg-indigo-500',
    dataKey: 'monthly_earning',
    hasDetails: false,
  },
]

const dailyStats = [
  {
    title: 'สมาชิกวันนี้',
    icon: UserGroupIcon,
    bgColor: 'bg-blue-500',
    dataKey: 'daily_player',
    hasDetails: true,
  },
  {
    title: 'รับเครดิตฟรีวันนี้',
    icon: GiftIcon,
    bgColor: 'bg-green-500',
    dataKey: 'daily_free_credit',
    hasDetails: false,
  },
  {
    title: 'จำนวนเครดิตฟรีที่เสียวันนี้',
    icon: ArrowTrendingDownIcon,
    bgColor: 'bg-red-500',
    dataKey: 'daily_free_credit_lose',
    hasDetails: false,
  },
  {
    title: 'ยอดฝากรวมวันนี้',
    icon: CurrencyDollarIcon,
    bgColor: 'bg-yellow-500',
    dataKey: 'daily_deposit',
    hasDetails: true,
  },
  {
    title: 'ยอดถอนรวมวันนี้',
    icon: ArrowUturnUpIcon,
    bgColor: 'bg-purple-500',
    dataKey: 'daily_withdraw',
    hasDetails: true,
  },
  {
    title: 'สรุปรายได้วันนี้',
    icon: ChartBarIcon,
    bgColor: 'bg-indigo-500',
    dataKey: 'daily_earning',
    hasDetails: false,
  },
]
</script>
