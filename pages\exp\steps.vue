<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Steps" />
    <Steps
      v-slot="{ option }"
      :value="step"
      :options="stepOptions"
      @update:model-value="step = $event"
    >
      <div v-if="option.value === 'first-step'" class="p-4">
        {{ option.label }} - {{ option.value }}
      </div>
      <div v-if="option.value === 'second-step'" class="p-4">
        {{ option.label }} - {{ option.value }}
      </div>
      <div v-if="option.value === 'third-step'" class="p-4">
        {{ option.label }} - {{ option.value }}
      </div>
    </Steps>
    <div class="mt-4 flex justify-end gap-4">
      <Button :disabled="step === 0" @click="step--">Previous Step</Button>
      <Button class="btn-primary" :disabled="step === stepOptions.steps.length - 1" @click="step++">
        Next Step
      </Button>
    </div>
  </NuxtLayout>
</template>

<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'
import { IStepOptions } from '~/components/Steps/types'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.steps.name })
app.updatePageMeta({
  title: styleguideMenu.steps.name,
  breadcrumbs: [styleguideMenu.steps],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))

const step = ref(0)

const stepOptions = computed<IStepOptions>(() => ({
  steps: [
    {
      label: 'อัพโหลดไฟล์',
      value: 'first-step',
    },
    {
      label: 'แสดงรายละเอียด',
      value: 'second-step',
    },
    {
      label: 'จัดการข้อมูล',
      value: 'third-step',
    },
  ],
  isStepClickable: true,
  isNotChangeRoute: true,
}))
</script>
