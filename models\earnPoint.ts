import { type IAgentItem } from '~/models/agent'
import { type IPlayerItem } from '~/models/player'

export interface IEarnPointItem {
  id: string
  created_at: string
  updated_at: string
  agent_id: string
  title: string
  description: string
  image_url: string
  point: number
  type: number
  credit: number
}

export interface IRedeemHistoryItem {
  id: string
  created_at: string
  updated_at: string
  player_id: string
  player: IPlayerItem
  agent_id: string
  agent: IAgentItem
  reward_id: string
  status: string
  reward: IEarnPointItem
}
