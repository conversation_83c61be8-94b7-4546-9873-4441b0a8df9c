import { defineNuxtRouteMiddleware, navigateTo } from '#app'
import { useLogout, useMe } from '~/hooks/useAuth'
import { CONFIG } from '~/constants/config'
import { useCookie7Days } from '~/hooks/useCookie'
import { routes } from '~/constants/routes'

export default defineNuxtRouteMiddleware(async () => {
  const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)
  const me = useMe()

  if (token.value) {
    await me.run(undefined, { expire: 30 * 1000 })

    if (me.status.isError) {
      const logout = useLogout()

      await logout.run()

      return navigateTo(routes.login.to)
    }
  }
})
