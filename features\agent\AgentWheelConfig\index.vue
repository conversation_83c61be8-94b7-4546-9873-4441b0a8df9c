<template>
  <Loader :is-loading="wheelConfig.status.value.isLoading">
    <PageHeader :title="routes.agentWheelConfig.name" />
    <div class="card p-6 max-w-5xl mx-auto">
      <FormConfig
        :item="wheelConfig.data.value"
        :status="wheelConfigUpdate.status.value"
        @submit="wheelConfigUpdate.run"
      />
    </div>
  </Loader>
</template>
<script lang="tsx" setup>
import FormConfig from '~/features/agent/AgentWheelConfig/FormConfig.vue'
import { useAgentWheelConfigLoader, useAgentWheelConfigUpdateLoader } from '~/loaders/agentWheel'
import { routes } from '~/constants/routes'

const wheelConfig = useAgentWheelConfigLoader()
const wheelConfigUpdate = useAgentWheelConfigUpdateLoader()
const dialog = useDialog()

wheelConfig.setLoading()
onMounted(() => {
  wheelConfig.run()
})

useWatchTrue(
  () => wheelConfigUpdate.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'บันทึกข้อมูลเรียบร้อย',
      })
      .then(() => {
        wheelConfig.run()
      })
  }
)

useWatchTrue(
  () => wheelConfigUpdate.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(wheelConfigUpdate.status.value.errorData),
    })
  }
)
</script>
