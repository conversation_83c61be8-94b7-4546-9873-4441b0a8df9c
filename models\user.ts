import { type IAgentItem } from '~/models/agent'
import { type IPermissionItem } from '~/models/permission'

export const enum IUserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  SUPER_AGENT = 'super_agent',
  AGENT = 'agent',
}

export interface IUserItem {
  id: string
  agent_id: string
  username: string
  full_name: string
  role: IUserRole
  last_active: string
  last_ip: string
  created_at: string
  updated_at: string
  is_enabled: boolean
  agent?: IAgentItem
  permission?: IPermissionItem[]
}
