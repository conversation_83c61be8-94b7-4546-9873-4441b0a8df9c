<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Alert" />
    <div class="my-3 mx-auto bg-white p-8 rounded-10">
      <Alert type="success" title="Success" text="success" />
      <Alert type="danger" title="Danger" text="danger" />
      <Alert type="warning" title="Warning" text="warning" />
      <Alert type="info" title="Info" text="info" />
      <hr />
      <Alert type="success" title="Custom Icon" text="Custom Icon" icon="bell-solid" />
      <Alert type="danger" text="no title" icon="bell-solid" />
      <Alert type="info" title="no text" icon="bell-solid" />
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.alert.name })
app.updatePageMeta({
  title: styleguideMenu.alert.name,
  breadcrumbs: [styleguideMenu.alert],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))
</script>
