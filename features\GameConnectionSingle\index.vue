<template>
  <PageHeader :title="game.findItem?.name || routes.connections.name" />
  <Form @submit="onSubmit">
    <div class="text-xl font-semibold mb-2">ข้อมูลทั่วไป</div>
    <div class="p-3 border border-primary-900 border-dashed rounded-lg bg-white mb-6">
      <FormFields class="grid grid-cols-2 gap-4 mb-4" :options="generalFormFields()" />
    </div>
    <div class="text-xl font-semibold mb-2">ตั้งค่าการเชื่อมต่อ</div>
    <div class="p-3 border border-primary-900 border-dashed rounded-lg bg-white mb-6">
      <FormFields class="grid grid-cols-2 gap-4" :options="connectionFormFields()" />
    </div>
    <Button type="submit" :is-loading="game.updateStatus.isLoading">บันทึก</Button>
  </Form>
</template>
<script lang="tsx" setup>
import * as yup from 'yup'
import { useGameConnectionPageStore, useWatchTrue } from '#imports'
import { INPUT_TYPES } from '~/components/Form/types'
import { useForm } from 'vee-validate'

const route = useRoute()
const game = useGameConnectionPageStore()

const dialog = useDialog()
const { handleSubmit, setValues } = useForm<Partial<IForm>>()

onMounted(() => {
  setValues(game.findItem)
})

useWatchTrue(
  () => game.updateStatus.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'บันทึกข้อมูลเรียบร้อย',
      })
      .then(() => {
        game.find(route.params.id as string)
      })
  }
)

useWatchTrue(
  () => game.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(game.updateStatus.errorData),
    })
  }
)

const generalFormFields = () => {
  return [
    {
      type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
      class: 'col-span-full',
      props: {
        label: 'รูปภาพ',
        name: 'image_url',
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'name',
        name: 'name',
        placeholder: 'name',
        rules: yup.string().required(),
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'slug',
        name: 'slug',
        placeholder: 'slug',
        rules: yup.string().required(),
        disabled: true,
      },
    },
  ]
}

const connectionFormFields = () => {
  if (game.findItem?.slug === 'awc') {
    return [
      {
        type: INPUT_TYPES.TEXT,
        class: 'col-span-full',
        props: {
          label: 'url',
          name: 'connection.url',
          placeholder: 'url',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'agent id',
          name: 'connection.agent_id',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'cert',
          name: 'connection.cert',
          placeholder: 'cert',
          rules: yup.string().required(),
        },
      },
    ]
  } else if (game.findItem?.slug === 'joker') {
    return [
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'game url',
          name: 'connection.game_url',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'seamless url',
          name: 'connection.seamless url',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'key',
          name: 'connection.key',
          placeholder: 'key',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'app id',
          name: 'connection.app_id',
          placeholder: 'app id',
          rules: yup.string().required(),
        },
      },
    ]
  } else if (game.findItem?.slug === 'jili') {
    return [
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'url',
          name: 'connection.url',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'agent id',
          name: 'connection.agent_id',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'agent key',
          name: 'connection.agent_key',
          rules: yup.string().required(),
        },
      },
    ]
  } else if (game.findItem?.slug === 'pgslot') {
    return [
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'url',
          name: 'connection.url',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'agent id',
          name: 'connection.agent_id',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'agent key',
          name: 'connection.agent_key',
          rules: yup.string().required(),
        },
      },
    ]
  } else if (game.findItem?.slug === 'qtech') {
    return [
      {
        type: INPUT_TYPES.TEXT,
        class: 'col-span-full',
        props: {
          label: 'url',
          name: 'connection.url',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'username',
          name: 'connection.username',
          rules: yup.string().required(),
        },
      },
      {
        type: INPUT_TYPES.TEXT,
        props: {
          label: 'password',
          name: 'connection.password',
          rules: yup.string().required(),
        },
      },
    ]
  }

  return []
}

const onSubmit = handleSubmit((values) => {
  game.update(game.findItem?.id, values)
})

useWatchTrue(
  () => game.updateStatus.isSuccess,
  () => {
    window.location.reload()
  }
)

useWatchTrue(
  () => game.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(game.updateStatus.errorData?.message),
    })
  }
)
</script>
