<template>
  <PageHeader title="แก้ไขประกาศเว็บ" />
  <div class="flex justify-end">
    <Modal v-model="isShowCreateModal" class="modal-md" title="สร้างผู้เล่น">
      <CreateForm :status="notice.addStatus.value" @submit="notice.add" />
    </Modal>
    <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">สร้าง</Button>
  </div>
  <Table :options="tableOptions" @pageChange="notice.fetch" @search="notice.search" />
</template>
<script lang="tsx" setup>
import { useAgentNoticeLoader } from '~/loaders/agent'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { IAgentNoticeItem } from '~/models/agent'
import CreateForm from '~/features/agent/AgentNotice/CreateForm.vue'

const notice = useAgentNoticeLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)

notice.setFetchLoading()
onMounted(() => {
  notice.fetch()
})

const tableOptions = useTable<IAgentNoticeItem>({
  repo: notice,
  columns: () => [
    {
      value: 'รูปภาพ',
    },
    {
      value: 'ประเภท',
    },
    {
      value: 'แท็ก',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.image_urls,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.type,
        },
        {
          value: item.tag,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: '',
          type: COLUMN_TYPES.ACTION,
          props: {
            isHideEdit: true,
          },
          on: {
            delete: () => {
              notice.remove(item.id)
            },
          },
        },
      ]
    }),
  options: () => ({
    isHideToolbar: true,
  }),
})

useWatchTrue(
  () => notice.addStatus.value.isSuccess,
  () => {
    isShowCreateModal.value = false
    notice.fetch()
  }
)

useWatchTrue(
  () => notice.deleteStatus.value.isSuccess,
  () => {
    notice.fetch()
  }
)

useWatchTrue(
  () => notice.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(notice.addStatus.value.errorData),
    })
  }
)

useWatchTrue(
  () => notice.deleteStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(notice.addStatus.value.errorData),
    })
  }
)
</script>
