<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      แอดโปรโมชั่น
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { IStatus } from '~/lib/api/types'
import { INPUT_TYPES } from '~/components/Form/types'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const form = useForm()
const promotion = useAgentPromotionAllLoader()

promotion.setLoading()
onMounted(() => {
  promotion.run()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      class: 'mb-3',
      label: 'โปรโมชั่น',
      name: 'promotion_id',
      placeholder: 'เลือกโปรโมชั่น*',
      rules: yup.string().required(),
      isLoading: promotion.status.isLoading,
      options: ArrayHelper.toOptions(promotion.items, 'id', 'name'),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
