<template>
  <div>
    <Badge v-if="getValue" class="bg-success">ปกติ</Badge>
    <Badge v-else class="bg-danger">ปิดใช้งาน</Badge>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue'
import { IRowItem } from '../types'
import { computed } from '#imports'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<unknown>>,
    required: true,
  },
})

const getValue = computed<boolean>(() => {
  return ParamHelper.getBoolFalse(props.item.value)
})
</script>
