<template>
  <SwitchGroup :class="isLoading ? 'pointer-events-none' : ''">
    <div class="flex justify-center items-center gap-2">
      <Switch
        :value="innerValue ? `checked` : ``"
        :class="['switch-control h-7 w-11 cursor-pointer', innerValue ? 'active' : 'inactive']"
        @click="onChange"
      >
        <span
          :class="[
            'switch-control-inner !h-4 !w-4 !mt-[0.15rem]',
            innerValue ? 'translate-x-4 bg-primary' : 'translate-x-1 bg-gray-300',
          ]"
          aria-hidden="true"
        />
      </Switch>
      <Loader :is-loading="isLoading" class-name="w-3 h-3" />
    </div>
  </SwitchGroup>
</template>
<script lang="tsx" setup>
import { Switch, SwitchGroup } from '@headlessui/vue'
import { PropType } from 'vue'
import { ref } from '#imports'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps({
  action: {
    type: Function as PropType<(v: boolean) => Promise<void>>,
    required: true,
  },
  value: {
    type: Boolean,
    default: () => false,
  },
})

const innerValue = ref(false)
const isLoading = ref(false)

onMounted(() => {
  innerValue.value = props.value || false
})

const onChange = async () => {
  isLoading.value = true
  await props.action?.(!innerValue.value)
  innerValue.value = !innerValue.value
  isLoading.value = false
}
</script>
