<template>
  <div>
    <Menu v-slot="{ open }" as="div" class="relative inline-block text-left">
      <div>
        <MenuButton as="div">
          <Button
            :is-only-icon="true"
            class="btn-info"
            icon="gear-solid"
            @click="$emit('open', !open)"
          />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="fixed z-[99] mr-6 w-56 origin-left right-0 divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden"
        >
          <MenuItem>
            <div
              :class="[
                'group flex w-full items-center px-3 py-3',
                'text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="$emit('edit')"
            >
              แก้ไข
            </div>
          </MenuItem>
          <MenuItem>
            <div
              :class="[
                'group flex w-full items-center px-3 py-3',
                'text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="$emit('delete')"
            >
              ลบ
            </div>
          </MenuItem>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>
<script lang="tsx" setup>
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'

defineEmits(['open', 'edit', 'delete'])
</script>
