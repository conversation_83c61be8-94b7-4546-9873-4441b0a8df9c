<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      อัพเดต
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { IStatus } from '~/lib/api/types'
import { INPUT_TYPES } from '~/components/Form/types'
import { useBankListLoader } from '~/loaders/bank'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const form = useForm()
const bank = useBankListLoader()

bank.setLoading()
onMounted(() => {
  bank.run()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      class: 'mb-3',
      name: 'first_name',
      placeholder: 'กรอกชื่อจริง*',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'last_name',
      placeholder: 'กรอกนามสกุล*',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      class: 'mb-3',
      label: 'ธนาคาร',
      name: 'bank_id',
      placeholder: 'เลือกธนาคาร*',
      rules: yup.string().required(),
      isLoading: bank.status.value.isLoading,
      options: ArrayHelper.toOptions(bank.items.value, 'slug', 'name_th'),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'bank_number',
      placeholder: 'เลขบัญชีธนาคาร*',
      rules: yup.number().required(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
