import { useObjectLoader } from '~/lib/api/loaderObject'
import { type IAgentItem } from '~/models/agent'

export const useUserChangePasswordLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentItem, { new_password: string }>({
    method: 'post',
    url: `/admin/users/${userId}/change-password`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useUserEnabledLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentItem, { is_enabled: boolean }>({
    method: 'post',
    url: `/admin/users/${userId}/enabled`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentUserChangePasswordLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentItem, { new_password: string }>({
    method: 'post',
    url: `/agent/users/${userId}/change-password`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentUserEnabledLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentItem, { is_enabled: boolean }>({
    method: 'post',
    url: `/agent/users/${userId}/enabled`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
