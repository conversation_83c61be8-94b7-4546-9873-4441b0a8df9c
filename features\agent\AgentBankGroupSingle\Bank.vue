<template>
  <div class="flex items-center border border-gray-200 bg-white px-3 py-2">
    <img class="w-16 h-8 mr-4 object-contain" :src="bank.bank.image_url" :alt="bank.bank.name_th" />
    <div>
      <div class="font-semibold mb-1">
        {{ bank.account_name }}
      </div>
      <div class="font-semibold">
        {{ bank.account_number }}
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { PropType } from 'vue'
import { IBankItem } from '~/models/bank'

defineProps({
  bank: {
    type: Object as PropType<IBankItem>,
    required: true,
  },
})
</script>
