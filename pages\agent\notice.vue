<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentNotice />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentNotice from '~/features/agent/AgentNotice/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentNotice.name })
app.updatePageMeta({
  title: routes.agentNotice.name,
  breadcrumbs: [routes.agentNotice],
})
</script>
