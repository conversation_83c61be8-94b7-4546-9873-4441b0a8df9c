<template>
  <div>
    <Modal
      v-model="isShowEditModal"
      :no-backdrop-close="true"
      class="modal-md"
      :title="`แก้ไข ${item.props?.provider.provider.name}`"
    >
      <ProvidersEditForm
        :status="provider.updateStatus.value"
        :item="item.props.provider"
        @submit="onUpdateSubmit"
      />
    </Modal>
    <Menu as="div" class="relative inline-block text-left">
      <div>
        <MenuButton as="div">
          <Button :is-only-icon="true" class="btn-info" icon="gear-solid" />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute right-0 mt-2 z-20 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        >
          <div class="px-1 py-1">
            <p
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="isShowEditModal = true"
            >
              แก้ไข
            </p>
            <p
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white cursor-pointer',
              ]"
              @click="onToggleEnabled"
            >
              {{ item.props?.provider.is_enabled ? 'ปิดการใช้งาน' : 'เปิดการใช้งาน' }}
            </p>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>
<script lang="tsx" setup>
import { Menu, MenuButton, MenuItems } from '@headlessui/vue'
import { PropType } from 'vue'
import { IRowItem } from '~/components/Table/types'
import { useAgentProviderEnabledLoader } from '#imports'
import { IAgentProviderItem } from '~/models/provider'
import ProvidersEditForm from '~/features/AgentSingle/ProvidersEditForm.vue'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ provider: IAgentProviderItem }, { reload: () => void }>>,
    required: true,
  },
})

const route = useRoute()
const dialog = useDialog()
const provider = useAgentProviderPageLoader(route.params.id as string)
const isShowEditModal = ref(false)
const enabled = useAgentProviderEnabledLoader(
  route.params.id as string,
  props.item.props?.provider.id as string
)

const onToggleEnabled = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: `ต้องการ${
        props.item.props?.provider.is_enabled ? 'ปิดการใช้งาน' : 'เปิดการใช้งาน'
      } ${props.item.props?.provider.provider.name} ใช่หรือไม่`,
      cancelText: 'ยกเลิก',
      confirmText: 'ตกลง',
      isShowCancelBtn: true,
    })
    .then(() => {
      enabled.run({
        is_enabled: !props.item.props?.provider.is_enabled,
      })
    })
}

useWatchTrue(
  () => enabled.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: props.item.props?.provider.is_enabled
          ? 'เปิดการใช้งานสำเร็จ'
          : 'ปิดการใช้งานสำเร็จ',
      })
      .then(() => {
        props.item?.on?.reload()
      })
  }
)

useWatchTrue(
  () => enabled.status.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(enabled.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => provider.updateStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขข้อมูลสำเร็จ',
      })
      .then(() => {
        isShowEditModal.value = false
        props.item?.on?.reload()
      })
  }
)

useWatchTrue(
  () => provider.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(provider.updateStatus.value.errorData),
    })
  }
)

const onUpdateSubmit = (data: any) => {
  provider.update(props.item?.props?.provider.id as string, data)
}
</script>
