<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentWheelConfig />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentWheelConfig from '~/features/agent/AgentWheelConfig/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentWheelConfig.name })
app.updatePageMeta({
  title: routes.agentWheelConfig.name,
  breadcrumbs: [routes.agentWheelConfig],
})
</script>
