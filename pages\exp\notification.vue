<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Notification" />
    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Style</h1>
      <Button class="btn-success m-4" @click="onSuccess"> success </Button>
      <Button class="btn-danger m-4" @click="onError"> danger </Button>
      <Button class="btn-info m-4" @click="onInfo"> info </Button>
      <Button class="btn-warning m-4" @click="onWarning"> warning </Button>

      <div class="bg-gray-fill p-2 rounded-10 my-2">
        <pre>
        const noti = useNotification()
        const onSuccess = () => {
          noti.success({
            title: 'Success',
            text: 'description'
          })
        }
        const onError = () => {
          noti.error({
            title: 'error',
            text: 'description'
          })
        }
        const onWarning = () => {
          noti.warning({
            title: 'warning',
            text: 'description'
          })
        }
        const onInfo = () => {
          noti.info({
            title: 'Info',
            text: 'description',
            icon: 'bell-solid'
          })
        }
        </pre>
      </div>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'
import { useNotification } from '~/hooks/useNotification'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.notification.name })
app.updatePageMeta({
  title: styleguideMenu.notification.name,
  breadcrumbs: [styleguideMenu.notification],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))

const noti = useNotification()

const onSuccess = () => {
  noti.success({
    title: 'Success',
    text: 'description',
  })
}

const onError = () => {
  noti.error({
    title: 'error',
    text: 'description',
  })
}

const onWarning = () => {
  noti.warning({
    title: 'warning',
    text: 'description',
  })
}

const onInfo = () => {
  noti.info({
    title: 'Info',
    text: 'description',
    icon: 'bell-solid',
  })
}
</script>
