<template>
  <Form class="flex flex-col divide-y-2" @submit="onSubmit">
    <div class="px-8 pb-4">
      <h3 class="text-xl mb-4 font-semibold">ตั้งค่าส่วนที่ 1 ตั้งค่าโปรโมชั่น</h3>
      <FormFields :options="promotionSettingFormFields" class="grid grid-cols-2 gap-x-4" />
    </div>
    <div class="px-8 py-4">
      <h3 class="text-xl mb-4 font-semibold">ตั้งค่าส่วนที่ 2 รายละเอียด</h3>
      <FormFields :options="detailFormFields" class="grid grid-cols-4 gap-x-4" />
    </div>
    <div class="px-8 py-4">
      <h3 class="text-xl mb-4 font-semibold">ตั้งค่าส่วนที่ 3 เงื่อนไขโปรโมชั่น</h3>
      <FormFields :options="conditionFormFields" class="grid grid-cols-4 gap-x-4" />
    </div>
    <div class="px-8 py-4">
      <h3 class="text-xl mb-4 font-semibold">
        ตั้งค่าส่วนที่ 4 อัตราการจ่าย{{
          form?.values?.bonus_type === 'percent' ? 'เปอร์เซ็น' : 'โบนัว'
        }}
      </h3>
      <div
        v-for="(field, index) in bonusRateFormFields"
        :key="index"
        class="bg-admin p-4 rounded-lg mb-4 flex space-x-4"
      >
        <FormFields :options="field.value" class="grid grid-cols-4 gap-x-4" />
        <Button
          icon="ic ic-trash-solid"
          is-only-icon
          class="bg-red-400 text-white flex-shrink-0 mt-6 w-[44px] h-[44px]"
          @click="onDeleteCondition(index)"
        />
      </div>
      <Button class="w-full mb-4" @click="onAddCondition"> เพิ่มเงื่อนไข</Button>
    </div>
    <div class="px-8 py-4">
      <h3 class="text-xl mb-4 font-semibold">
        ตั้งค่าส่วนที่ 5 เงื่อนไขทำเทิร์นและขั้นต่ำเติมเงิน
      </h3>
      <FormFields :options="turnoverFormFields" class="grid grid-cols-2 gap-x-4" />
    </div>
    <!--    <div class="px-8 py-4">-->
    <!--      <h3 class="text-xl mb-4 font-semibold">เงื่อนไขแต้มพิเศษ</h3>-->
    <!--      <FormFields :options="pointFormFields" class="grid grid-cols-2 gap-x-4" />-->
    <!--    </div>-->
    <div class="px-8 py-4">
      <h3 class="text-xl mb-4 font-semibold">ตั้งค่าส่วนที่ 6 กำหนดเกม</h3>
      <FormFields :options="providerFormFields" />
    </div>
    <div class="px-8 py-4">
      <h3 class="text-xl mb-4 font-semibold">ตั้งค่าส่วนที่ 7 รูปภาพ</h3>
      <FormFields :options="imageFormFields" class="grid grid-cols-2 gap-x-4" />
    </div>
    <div class="px-8 py-4">
      <div class="my-4">
        <Button
          type="submit"
          class="btn-primary w-full btn-min"
          :is-loading="item ? promotion.updateStatus.isLoading : promotion.addStatus.isLoading"
        >
          {{ item ? 'แก้ไข' : 'สร้าง' }}
        </Button>
      </div>
    </div>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import dayjs from 'dayjs'
import { IFormField, INPUT_TYPES } from '~/components/Form/types'
import { createFormFields } from '~/hooks/useForm'
import { IPromotionItem } from '~/models/promotion'
import { PromotionType } from '~/constants/promotion'

const props = defineProps<{
  item?: IPromotionItem
}>()

const emits = defineEmits(['success'])

const promotion = useAgentPromotionPageLoader()
const provider = useProviderPageLoader()
const dialog = useDialog()

const initValue = ref<Partial<IPromotionItem>>({
  promotion_type: PromotionType.NORMAL,
  due_date_type: DueDateType.NONE,
  bonus_type: BonusType.PERCENT,
})

const form = useForm<Partial<IPromotionItem>>({
  initialValues: props.item ? props.item : initValue.value,
})

const conditionFields = ref<number[]>([])

onMounted(() => {
  const conditions = props.item?.conditions

  if (conditions?.length) {
    conditionFields.value = Array.from({ length: conditions.length }, (_, index) => index)
  }

  provider.fetch(1, undefined, {
    params: {
      limit: 1000,
    },
  })
})

const promotionSettingFormFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    class: 'col-span-2',
    props: {
      name: 'promotion_type',
      label: 'ประเภทโปรโมชั่น',
      options: promotionTypeOptions,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    class:
      form.values.promotion_sub_type === PromotionSubType.WELCOME_BACK
        ? 'col-span-1'
        : 'col-span-2',
    isHide: form.values.promotion_type !== PromotionType.STACK_TOP_UP,
    props: {
      name: 'promotion_sub_type',
      label: 'ชนิดโปรโมชั่น',
      options: promotionSubTypeOptions,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    class: 'col-span-1',
    isHide: form.values.promotion_sub_type !== PromotionSubType.WELCOME_BACK,
    props: {
      name: 'idle_deposit_day',
      label: 'ลูกค้าไม่เติมเงินครบ',
      type: 'number',
      rules: yup.number().min(0).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">วัน</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('idle_deposit_day', parseInt(Number(value).toFixed(0), 10))
      },
    },
  },
])

const detailFormFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name_th',
      label: 'ชื่อโปรโมชั่น (ภาษาไทย)',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'topic_th',
      label: 'หัวข้อ (ภาษาไทย)',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name_en',
      label: 'ชื่อโปรโมชั่น (ภาษาอังกฤษ)',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'topic_en',
      label: 'หัวข้อ (ภาษาอังกฤษ)',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      name: 'is_active',
      label: 'เปิดใช้งานโปรโมชั่น',
      rules: yup.boolean(),
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      name: 'is_display',
      label: 'แสดงหน้าโปรโมชั่น',
      rules: yup.boolean(),
    },
  },
  {
    type: INPUT_TYPES.WYSIWYG,
    class: 'col-start-1 col-span-2',
    props: {
      name: 'description_th',
      label: 'รายละเอียด (ภาษาไทย)',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.WYSIWYG,
    class: 'col-span-2',
    props: {
      name: 'description_en',
      label: 'รายละเอียด (ภาษาอังกฤษ)',
      rules: yup.string().required(),
    },
  },
])

const conditionFormFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'bonus_type',
      label: 'ประเภทโบนัส',
      options: bonusTypeOptions,
      rules: yup.string().required(),
    },
    on: {
      change: (_value: string) => {
        form.setFieldValue('conditions', [])
        conditionFields.value = []
      },
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'user_type_receiver',
      label: 'ประเภทผู้เล่นที่รับโปรโมชั่น',
      options: userTypeReceiverOptions,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'redeem_by_type',
      label: 'ประเภทการเติมโปรโมชั่น',
      options: redeemByTypeOptions,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'auto_withdraw_status',
      label: 'สถานะการถอนอัตโนมัติ',
      options: withdrawStatusOptions,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    isHide: form.values.promotion_type !== PromotionType.WELCOME_BACK,
    props: {
      name: 'idle_deposit_day',
      label: 'ลูกค้าไม่เติมเงินครบ * วัน',
      type: 'number',
      rules: yup.number().min(0).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">วัน</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('idle_deposit_day', parseInt(Number(value).toFixed(0), 10))
      },
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'due_date_type',
      label: 'เงื่อนไขวันที่',
      options: dueDateTypeOptions,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.DATE_TIME,
    class: 'col-start-1 col-span-2',
    isHide: form.values.due_date_type === DueDateType.NONE,
    props: {
      name: 'start_date',
      label: 'วันที่เริ่มต้น',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.DATE_TIME,
    class: 'col-span-2',
    isHide: form.values.due_date_type === DueDateType.NONE,
    props: {
      name: 'end_date',
      label: 'วันที่สิ้นสุด',
      rules: yup.string().required(),
    },
  },
])

const bonusRateFormFields = computed(() => {
  const bonusType = form.values.bonus_type

  const typePercentType = (index: number): IFormField[] => [
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].min_deposit_amount`,
        label: 'เติมเงินขั้นต่ำ',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].min_deposit_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].min_deposit_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].max_deposit_amount`,
        label: 'เติมเงินสูงสุด',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].max_deposit_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].max_deposit_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].percent_payout`,
        label: 'เปอร์เซ็นการจ่าย',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">%</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].percent_payout` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].percent_payout` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].max_withdraw_amount`,
        label: 'ถอนเงินสูงสุด',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].max_withdraw_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].max_withdraw_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
  ]

  const typeBonusType = (index: number): IFormField[] => [
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].min_deposit_amount`,
        label: 'เติมเงินขั้นต่ำ',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].min_deposit_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].min_deposit_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].max_deposit_amount`,
        label: 'เติมเงินสูงสุด',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].max_deposit_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].max_deposit_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].bonus_payout_amount`,
        label: 'โบนัส',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].bonus_payout_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].bonus_payout_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `conditions[${index}].max_withdraw_amount`,
        label: 'ถอนเงินสูงสุด',
        rules: yup.number().min(0).required(),
        appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
      },
      on: {
        blur: (value: string) => {
          const field = `conditions[${index}].max_withdraw_amount` as keyof IPromotionItem

          form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
        },
        mounted: (value: string) => {
          const field = `conditions[${index}].max_withdraw_amount` as keyof IPromotionItem

          if (value) {
            form.setFieldValue(field, parseFloat(Number(value).toFixed(2)))
          }
        },
      },
    },
  ]

  return conditionFields.value.map((index) =>
    createFormFields(() => {
      if (form.values.promotion_type === PromotionType.DEPOSIT_CONTINUE) {
        return depositContinueType(index)
      }

      return bonusType === BonusType.PERCENT ? typePercentType(index) : typeBonusType(index)
    })
  )
})

const turnoverFormFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'slot_turnover',
      label: 'ทำเทิร์นสล็อตตอนถอนเครดิต',
      rules: yup.number().min(1).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">ครั้ง</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('slot_turnover', parseFloat(Number(value).toFixed(2)))
      },
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'baccarat_turnover',
      label: 'ทำเทิร์นบาคาร่าตอนถอนเครดิต',
      rules: yup.number().min(1).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">ครั้ง</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('baccarat_turnover', parseFloat(Number(value).toFixed(2)))
      },
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'min_deposit_amount',
      label: 'เติมเงินขั้นต่ำที่จะได้รับโปรโมชั่น',
      rules: yup.number().min(0).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('min_deposit_amount', parseFloat(Number(value).toFixed(2)))
      },
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'max_deposit_amount',
      label: 'โบนัสสูงสุดที่ลูกค้าจะได้รับ',
      rules: yup.number().min(0).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('max_deposit_amount', parseFloat(Number(value).toFixed(2)))
      },
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'max_bonus_payout_amount',
      label: 'โบนัสสูงสุดที่ลูกค้าจะได้รับ',
      rules: yup.number().min(0).required(),
      appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
    },
    on: {
      blur: (value: string) => {
        form.setFieldValue('max_bonus_payout_amount', parseFloat(Number(value).toFixed(2)))
      },
    },
  },
])

// const pointFormFields = createFormFields(() => [
//   {
//     type: INPUT_TYPES.TEXT,
//     props: {
//       name: 'min_deposit_point',
//       label: 'เติมเงินขั้นต่ำที่จะได้รับแต้มพิเศษ',
//       rules: yup.number().min(0).required(),
//       appendIcon: defineComponent(() => () => <span class="text-sm">บาท</span>),
//     },
//     on: {
//       blur: (value: string) => {
//         form.setFieldValue('min_deposit_point', parseFloat(Number(value).toFixed(2)))
//       },
//     },
//   },
//   {
//     type: INPUT_TYPES.TEXT,
//     props: {
//       name: 'point_to_player',
//       label: 'แต้มที่จะได้รับ',
//       rules: yup.number().min(0).required(),
//       appendIcon: defineComponent(() => () => <span class="text-sm">แต้ม</span>),
//     },
//     on: {
//       blur: (value: string) => {
//         form.setFieldValue('point_to_player', parseFloat(Number(value).toFixed(2)))
//       },
//     },
//   },
// ])

const providerFormFields = createFormFields(() => [
  {
    type: INPUT_TYPES.MULTI_SELECT,
    props: {
      name: 'provider_ids',
      label: 'ค่ายเกม',
      options: ArrayHelper.toOptions(provider.fetchItems.value, 'id', 'name'),
      rules: yup.array().min(1).required(),
    },
  },
])

const imageFormFields = createFormFields(() => [
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      name: 'image_url_th',
      label: 'รูปภาพ (ภาษาไทย)',
      accept: '.jpg,.jpeg,.png',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      name: 'image_url_en',
      label: 'รูปภาพ (ภาษาอังกฤษ)',
      accept: '.jpg,.jpeg,.png',
      rules: yup.string().required(),
    },
  },
])

// const displayFormFields = createFormFields(() => [
//   {
//     type: INPUT_TYPES.TOGGLE_SWITCH,
//     props: {
//       name: 'is_active',
//       label: 'เปิดใช้งานโปรโมชั่น',
//       rules: yup.boolean(),
//     },
//   },
//   {
//     type: INPUT_TYPES.TOGGLE_SWITCH,
//     props: {
//       name: 'is_display',
//       label: 'แสดงหน้าโปรโมชั่น',
//       rules: yup.boolean(),
//     },
//   },
// ])

const onAddCondition = () => {
  conditionFields.value.push(conditionFields.value.length)
}

const onDeleteCondition = (index: number) => {
  conditionFields.value.pop()
  form.values.conditions?.splice(index, 1)
}

const onSubmit = form.handleSubmit((payload) => {
  if (payload.start_date) {
    payload.start_date = dayjs(payload.start_date).utc().format('YYYY-MM-DD HH:mm:ss')
  }

  if (payload.end_date) {
    payload.end_date = dayjs(payload.end_date).utc().format('YYYY-MM-DD HH:mm:ss')
  }

  if (payload.provider_ids && payload.provider_ids.length > 0) {
    payload.provider_ids = payload.provider_ids?.map((item: any) => item.value)
  }

  if (props.item) {
    dialog
      .warning({
        title: 'ยืนยันต้องการแก้ไขโปรโมชั่น',
        isShowCancelBtn: true,
      })
      .then(() => {
        promotion.update(props.item!.id, payload)
      })

    return
  }

  promotion.add(payload)
})

useWatchTrue(
  () => provider.fetchStatus.value.isSuccess,
  () => {
    const providerIDs = props.item?.provider_ids

    setTimeout(() => {
      form.setFieldValue('provider_ids', providerIDs)
    }, 100)
  }
)

useWatchTrue(
  () => promotion.addStatus.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างโปรโมชั่นสำเร็จ',
      })
      .then(() => {
        emits('success')
      })
  }
)

useWatchTrue(
  () => promotion.addStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(promotion.addStatus.errorData),
    })
  }
)

useWatchTrue(
  () => promotion.updateStatus.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขโปรโมชั่นสำเร็จ',
      })
      .then(() => {
        emits('success')
      })
  }
)

useWatchTrue(
  () => promotion.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(promotion.updateStatus.errorData),
    })
  }
)
</script>
