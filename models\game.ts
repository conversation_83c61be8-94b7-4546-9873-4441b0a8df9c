import {type IAgentItem} from '~/models/agent'
import {type IProviderItem} from '~/models/provider'
import {type IPlayerItem} from '~/models/player'

export interface IGameTransactionItem {
  id: string
  created_at: string
  updated_at: string
  agent_id: string
  player_id: string
  game_type: string
  game_code: string
  platform: string
  tx_id: string
  round_id: string
  bet_type: string
  currency: string
  bet_time: string
  bet_amount: number
  after_credit: number
  before_credit: number
  win_amount: number
  turnover: number
  winner: string
  odds: number
  win_loss: number
  result: string
  status: string
  agent: IAgentItem
  provider: IProviderItem
  player: IPlayerItem
  is_feature: boolean
  is_free_round: boolean
  game: any
}

export interface IGameSummaryItem {
  total_bet: number
  total_win: number
  total_diff: number
  total_provider_share: number
  total_profit: number
  provider: IProviderItem
}
