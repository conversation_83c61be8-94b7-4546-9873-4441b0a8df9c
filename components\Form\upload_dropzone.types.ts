import { type IFieldProps, type IFormFieldBase, type INPUT_TYPES } from '~/components/Form/types'

export interface IUploadDropzoneProps extends IFieldProps {
  dropzoneClassName?: ClassName
  accept?: string[] | string
  // in kb
  maxSize?: number
}

export type IUploadDropzone = IFormFieldBase<
  INPUT_TYPES.UPLOAD_DROPZONE | INPUT_TYPES.UPLOAD_DROPZONE_MULTI,
  IUploadDropzoneProps,
  {
    change: (file: File | File[]) => void
  }
>
