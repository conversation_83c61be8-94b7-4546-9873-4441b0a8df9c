<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Button" />
    <div class="text-description mb-6">
      กำหนดสไตล์ปุ่ม ให้กำหนดผ่านทาง
      <code class="bg-primary-200 p-2 rounded text-dark">class="btn-xxxx" </code>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Color Style</h1>
      <Button class="btn-primary m-4"> ตัวอย่างปุ่ม (btn-primary) </Button>
      <Button class="btn-danger m-4"> btn-danger </Button>
      <Button class="btn-success m-4"> btn-success </Button>
      <Button class="btn-info m-4"> btn-info </Button>
      <Button class="btn-warning m-4"> btn-warning </Button>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Outline Button</h1>
      <Button class="btn-primary-outline m-4"> btn-primary-outline </Button>
      <Button class="btn-danger-outline m-4"> btn-danger-outline </Button>
      <Button class="btn-success-outline m-4"> btn-success-outline </Button>
      <Button class="btn-info-outline m-4"> btn-info-outline </Button>
      <Button class="btn-warning-outline m-4"> btn-warning-outline </Button>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Plain Button</h1>
      <Button class="btn-primary-plain m-4"> btn-primary-plain </Button>
      <Button class="btn-danger-plain m-4"> btn-danger-plain </Button>
      <Button class="btn-success-plain m-4"> btn-success-plain </Button>
      <Button class="btn-info-plain m-4"> btn-info-plain </Button>
      <Button class="btn-warning-plain m-4"> btn-warning-plain </Button>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-4">
        Icon button
        <code class="bg-primary-200 p-2 rounded text-dark text-base ml-2">icon="house-solid"</code>
      </h1>
      <Button class="btn-primary m-4" icon="house-solid"> หน้าหลัก </Button>
      <Button class="btn-primary-outline m-4" icon="house-solid"> หน้าหลัก </Button>
      <h1 class="display-heading mb-4 mt-8">
        Only Icon button
        <code class="bg-primary-200 p-2 rounded text-dark text-base ml-2">:isOnlyIcon="true"</code>
      </h1>
      <Button :is-only-icon="true" class="btn-primary m-1" icon="trash-solid" />
      <Button :is-only-icon="true" class="btn-primary-outline m-1" icon="trash-solid" />
      <Button :is-only-icon="true" class="btn-primary-plain m-1" icon="trash-solid" />
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Sizing</h1>
      <div class="flex items-end">
        <Button class="btn-sm m-4"> Button Small (btn-sm) </Button>
        <Button class="btn-md m-4"> Button Medium (btn-md) </Button>
        <Button class="btn-lg m-4"> Button Large (btn-lg) </Button>
      </div>
      <div class="flex items-end mt-4">
        <Button :is-only-icon="true" class="btn-primary btn-sm m-4" icon="trash-solid" />
        <Button :is-only-icon="true" class="btn-primary btn-md m-4" icon="trash-solid" />
        <Button :is-only-icon="true" class="btn-primary btn-lg m-4" icon="trash-solid" />
      </div>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-4">
        Disabled
        <code class="bg-primary-200 p-2 rounded text-dark text-base ml-2">:isDisabled="true"</code>
      </h1>
      <Button :is-disabled="true" class="m-4"> Disabled Button </Button>
      <Button :is-disabled="true" class="btn-primary m-4"> Disabled Button </Button>
      <Button :is-disabled="true" class="btn-primary-outline m-4"> Disabled Button </Button>
      <Button :is-disabled="true" class="btn-primary-plain m-4"> Disabled Button </Button>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-4">
        Loading button
        <code class="bg-primary-200 p-2 rounded text-dark text-base ml-2">:isLoading="true"</code>
      </h1>
      <Button :is-loading="true" class="btn-primary m-4" icon="user"> ผู้ใช้งาน </Button>
      <Button :is-loading="true" class="btn-primary-outline m-4" icon="user"> ผู้ใช้งาน </Button>
      <Button :is-loading="true" :is-only-icon="true" class="btn-primary m-4" icon="user" />
      <Button :is-loading="true" :is-only-icon="true" class="btn-primary-outline m-4" icon="user" />
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.button.name })
app.updatePageMeta({
  title: styleguideMenu.button.name,
  breadcrumbs: [styleguideMenu.button],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))
</script>
