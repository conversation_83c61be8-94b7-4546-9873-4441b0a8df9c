.form-control {
  @apply relative appearance-none ring-1 ring-gray px-3 ring-inset py-3 text-dark font-normal bg-white rounded-md text-sm
  placeholder-gray-disabled min-h-[44px] focus:outline-none focus:ring-primary w-full disabled:ring-gray-border disabled:bg-gray-fill;

  &[data-headlessui-state='open'] {
    @apply ring-primary;
  }

  &.disabled {
    @apply ring-gray-border bg-gray-fill;
  }
}

.form-control-static {
  @apply w-full block text-gray-700 py-2;
}


.textarea-max-counter {
  @apply block text-right text-xs text-gray-disabled mt-1 font-light;
}


.prepend-icon {
  @apply flex items-center justify-center h-[44px] px-3;
}

.append-icon {
  @apply flex items-center justify-center h-[44px] px-3;
}
