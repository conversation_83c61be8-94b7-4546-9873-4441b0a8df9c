<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <GameConnection />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import GameConnection from '~/features/GameConnection/index.vue'

const app = useApp()

definePageMeta({
  middleware: [MIDDLEWARES.AUTH_ADMIN],
})

app.updateDocMeta({ title: routes.connections.name })
app.updatePageMeta({
  title: routes.connections.name,
  breadcrumbs: [routes.connections],
})
</script>
