<template>
  <Loader :is-loading="agent.status.value.isLoading">
    <PageHeader title="ปรับแต่งเว็บไซต์" />
    <div class="card p-6 mx-auto">
      <FormConfig
        :item="agent.data.value?.website_config"
        :status="agentConfig.status.value"
        @submit="agentConfig.run"
      />
    </div>
  </Loader>
</template>
<script lang="tsx" setup>
import FormConfig from '~/features/agent/AgentWebConfig/FormConfig.vue'
import { useAgentConfigUpdateLoader } from '~/loaders/agent'

const agent = useAgentLoader()
const agentConfig = useAgentConfigUpdateLoader()
const dialog = useDialog()

agent.setLoading()
onMounted(() => {
  agent.run()
})

useWatchTrue(
  () => agentConfig.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ปรับแต่งเว็บไซต์สำเร็จ',
      })
      .then(() => {
        agent.run()
      })
  }
)

useWatchTrue(
  () => agentConfig.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(agentConfig.status.value.errorData),
    })
  }
)
</script>
