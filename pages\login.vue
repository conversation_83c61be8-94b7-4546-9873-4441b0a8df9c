<template>
  <NuxtLayout :name="LAYOUTS.NONE">
    <Login />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import Login from '~/features/Login/index.vue'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'

definePageMeta({
  middleware: MIDDLEWARES.NOT_AUTH,
})

const app = useApp()

app.updateDocMeta({ title: routes.login.name })
app.updatePageMeta({
  title: routes.login.name,
  breadcrumbs: [routes.login],
})
</script>
