import { type ISidebarItemState } from '~/hooks/useApp'
import { agentPermissions } from '~/constants/permissions'
import { type IPermissionItem } from '~/models/permission'

export const addPermissionToMenu = (
  menu: ISidebarItemState,
  permissions: string[]
): ISidebarItemState => {
  return {
    ...menu,
    permissions,
  }
}

export const routes = {
  home: {
    to: '/',
    name: 'หน้าแรก',
  },
  agentDashboard: {
    to: '/agent/dashboard',
    name: 'Dashboard',
  },
  dashboard: {
    to: '/dashboard',
    name: 'Dashboard',
  },
  login: {
    to: '/login',
    name: 'Login',
  },
  overview: {
    to: '/',
    name: 'Overview',
  },
  agents: {
    name: 'Agents',
    to: '/agents',
  },
  providers: {
    name: 'ค่ายเกม',
    to: '/providers',
  },
  connections: {
    name: 'การเชื่อมต่อ',
    to: '/game-connections',
  },
  agentCreditReports: {
    name: 'รายงานเครดิต',
    to: '/credit-reports',
  },
  agentEdit: (id: string, name = 'แก้ไข Agent') => ({
    name,
    to: `/agents/${id}`,
  }),
  providerEdit: (id: string, name = 'แก้ไขค่ายเกม') => ({
    name,
    to: `/providers/${id}`,
  }),
  connectionEdit: (id: string, name = 'แก้ไขการเชื่อมต่อ') => ({
    name,
    to: `/game-connections/${id}`,
  }),
  agentInfo: {
    name: 'ข้อมูล',
    to: '/agent/info',
  },
  agentPlayers: {
    name: 'ผู้เล่น',
    to: '/agent/players',
  },
  agentDeposits: {
    name: 'รายการฝาก',
    to: '/agent/deposits',
  },
  agentWithdraws: {
    name: 'รายการถอน',
    to: '/agent/withdraws',
  },
  agentPLayerEdit: (id: string, name = 'แก้ไขผู้เล่น') => ({
    name,
    to: `/agent/players/${id}`,
  }),
  agentWebConfig: {
    name: 'ปรับแต่งเว็บไซต์',
    to: '/agent/web-config',
  },
  agentWheelConfig: {
    name: 'ปรับแต่งกงล้อลุ้นโชค',
    to: '/agent/wheel-config',
  },
  agentNotice: {
    name: 'แก้ไขประกาศเว็บ',
    to: '/agent/notice',
  },
  agentAffiliates: {
    name: 'แชร์รับรายได้',
    to: '/agent/affiliates',
  },
  agentUsers: {
    name: 'รายชื่อผู้ช่วย',
    to: '/agent/users',
  },
  agentProviders: {
    name: 'ค่ายเกม',
    to: '/agent/providers',
  },
  agentBanks: {
    name: 'บัญชีธนาคาร',
    to: '/agent/banks',
  },
  agentBankEdit: (id: string, name = '') => ({
    name: name || 'บัญชีธนาคาร',
    to: `/agent/banks/${id}`,
  }),
  agentBankGroups: {
    name: 'กลุ่มบัญชีธนาคาร',
    to: '/agent/bank-groups',
  },
  agentBankGroupEdit: (id: string) => ({
    name: 'กลุ่มบัญชีธนาคาร',
    to: `/agent/bank-groups/${id}`,
  }),

  agentPayments: {
    name: 'Payment Gateway',
    to: '/agent/payments',
  },
  agentPaymentEdit: (id: string, name = '') => ({
    name: name || 'Payment Gateway',
    to: `/agent/payments/${id}`,
  }),
  agentAnnounce: {
    name: 'ประกาศ',
    to: '/agent/announces',
  },
  agentAnnounceEdit: (id: string, name = 'แก้ไขประกาศ') => ({
    name,
    to: `/agent/announces/${id}`,
  }),
  agentRoles: {
    name: 'ตั้งค่า Role',
    to: '/agent/roles',
  },
  agentGameTransactions: {
    name: 'ประวัติการเล่นเกม',
    to: '/agent/game-transactions',
  },
  agentGameSummary: {
    name: 'สรุปยอดเล่นเกม',
    to: '/agent/game-summary',
  },
  agentRedeemHistory: {
    name: 'ประวัติการแลกของรางวัล',
    to: '/agent/redeem-histories',
  },
  agentPromotions: {
    name: 'โปรโมชั่น',
    to: '/agent/promotions',
  },
  agentEarnPoints: {
    name: 'สะสมแต้ม',
    to: '/agent/earn-points',
  },
}

export const adminMenu: ISidebarItemState[] = [
  routes.overview,
  routes.providers,
  routes.connections,
  routes.agents,
  routes.agentCreditReports,
]

export const agentMenu: ISidebarItemState[] = [
  routes.agentDashboard,
  routes.agentInfo,
  {
    name: 'เกม',
    to: '',
    children: [
      addPermissionToMenu(routes.agentProviders, [agentPermissions.providerRead]),
      addPermissionToMenu(routes.agentGameTransactions, [agentPermissions.providerRead]),
      addPermissionToMenu(routes.agentGameSummary, [agentPermissions.providerRead]),
    ],
  },
  {
    name: 'ผู้เล่น',
    to: '',
    children: [
      addPermissionToMenu(routes.agentPlayers, [agentPermissions.playerRead]),
      addPermissionToMenu(routes.agentDeposits, [
        agentPermissions.playerRead,
        agentPermissions.playersReadDeposits,
      ]),
      addPermissionToMenu(routes.agentWithdraws, [
        agentPermissions.playerRead,
        agentPermissions.playersReadWithdraws,
      ]),
      addPermissionToMenu(routes.agentRedeemHistory, []),
    ],
  },
  {
    name: 'บัญชีธนาคาร',
    to: '',
    children: [
      addPermissionToMenu(routes.agentBanks, [agentPermissions.bankRead]),
      // addPermissionToMenu(routes.agentBankGroups, [agentPermissions.bankRead]),
      addPermissionToMenu(routes.agentPayments, [agentPermissions.bankRead]),
    ],
  },
  {
    name: 'การเข้าใช้งาน',
    to: '',
    children: [
      addPermissionToMenu(routes.agentUsers, [agentPermissions.userRead]),
      addPermissionToMenu(routes.agentRoles, [agentPermissions.roleRead]),
    ],
  },
  {
    name: 'ตั้งค่าทั่วไป',
    to: '',
    children: [
      addPermissionToMenu(routes.agentWheelConfig, []),
      addPermissionToMenu(routes.agentEarnPoints, []),
      addPermissionToMenu(routes.agentAffiliates, [
        agentPermissions.affiliateSettingsRead,
        agentPermissions.affiliateSettingsUpdate,
      ]),
      addPermissionToMenu(routes.agentPromotions, [
        agentPermissions.promotionsRead,
        agentPermissions.promotionsCreate,
        agentPermissions.promotionsUpdate,
      ]),
      addPermissionToMenu(routes.agentAnnounce, []),
      addPermissionToMenu(routes.agentWebConfig, [agentPermissions.webConfigUpdate]),
      addPermissionToMenu(routes.agentNotice, [agentPermissions.noticeUpdate]),
    ],
  },
]

export const styleguideMenu: Record<string, ISidebarItemState> = {
  styleguide: {
    name: 'สไตล์ไกด์',
    icon: 'star-solid',
    to: '/exp/styleguide',
  },
  pageHeader: {
    name: 'Page Header',
    icon: 'file-earmark-text-solid',
    to: '/exp/page-header',
  },
  modal: {
    name: 'หน้าต่างป็อปอัพ',
    icon: 'window-stack-outline',
    to: '/exp/modal',
  },
  button: {
    name: 'ปุ่มกด',
    icon: 'menu-button-solid',
    to: '/exp/button',
  },
  notification: {
    name: 'แจ้งเตือน',
    icon: 'exclamation-circle-solid',
    to: '/exp/notification',
  },
  form: {
    name: 'ฟอร์ม',
    icon: 'card-list-outline',
    to: '/exp/form',
  },
  table: {
    name: 'ตาราง',
    icon: 'table-solid',
    to: '/exp/table',
  },
  dialog: {
    name: 'ป็อปอัพแจ้งเตือน',
    icon: 'exclamation-square-solid',
    to: '/exp/dialog',
  },
  tab: {
    name: 'แท็บ',
    icon: 'table-column-solid',
    to: '/exp/tab',
  },
  alert: {
    name: 'แจ้งเตือน(Alert)',
    icon: 'exclamation-triangle-solid',
    to: '/exp/alert',
  },
  steps: {
    name: 'สเตปเปอร์',
    icon: 'bar-chart-steps-outline',
    to: '/exp/steps',
  },
  icon: {
    name: 'ไอคอน',
    icon: 'collection-solid',
    to: '/exp/icon',
  },
}

export const menuToSidebarItems = (menu: Record<string, ISidebarItemState>) => {
  return Object.keys(menu).map((key) => {
    return menu[key]
  })
}

export const filterMenuByPermissions =
  (permissions: IPermissionItem[]) =>
  (menu: ISidebarItemState): ISidebarItemState => {
    const permissionStringArray = permissions.map((permission) => permission.key)

    if (menu.children) {
      menu.children = menu.children.filter((child) => {
        if (child.permissions) {
          return child.permissions.every((permission) => permissionStringArray.includes(permission))
        }

        return true
      })
    }

    return menu
  }
