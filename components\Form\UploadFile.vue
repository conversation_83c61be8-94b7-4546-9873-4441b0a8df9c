<template>
  <FieldWrapper v-bind="wrapperProps">
    <div :class="['upload-file', fieldClassName]">
      <input
        ref="fileInput"
        type="file"
        :accept="acceptFile"
        :disabled="disabled"
        @change="handleChange"
      />
      <div :class="['upload-file-wrapper', uploadFileClassName]">
        <Button class="btn-sm btn-primary" @click="handleOpenFile">{{
          selectFileLabel || 'Select'
        }}</Button>
        <div class="upload-file-name">
          {{ selectedFile?.name ?? placeholder ?? 'No file selected' }}
        </div>
        <div v-if="selectedFile" class="upload-file-size">size {{ selectedFileSize }} MB</div>
        <div v-if="selectedFile">
          <IconLoading v-if="percent !== 0 && percent !== 100" class="text-primary w-5" />
          <em v-if="percent === 100" class="ic ic-check-circle-solid text-success w-5" />
          <em v-if="upload.status.value.isError" class="ic ic-x-circle-solid text-danger w-5" />
        </div>
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="tsx" setup>
import { IUploadFileProps } from './upload_file.types'
import FieldWrapper from './FieldWrapper.vue'

const props = withDefaults(defineProps<IUploadFileProps>(), {})

const {
  wrapperProps,
  fieldClassName,
  disabled,
  handleChange: onChange,
  setErrors,
  value,
} = useFieldHOC<string>(props)

const upload = useUploadLoader()

const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | undefined>()
const percent = ref<number>(0)

const selectedFileSize = computed(() => ((selectedFile.value?.size || 0) / 1000 / 1000).toFixed(2))
const acceptFileSize = computed(() => props.maxSize)
const acceptFile = computed(() =>
  typeof props.accept === 'string' ? props.accept : props.accept?.join(',')
)

useWatchTrue(
  () => upload.status.value.isSuccess,
  () => {
    if (upload.data.value?.url) {
      value.value = upload.data.value?.url
      onChange(upload.data.value?.url)
    }
  }
)

const handleOpenFile = () => {
  fileInput.value?.click()
}

const handleChange = (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0]
  const result = handleCheckFileCondition(file)

  if (result && file) {
    selectedFile.value = file
    const formData = new FormData()

    formData.append('file', file)
    upload.run(formData, { data: { onUploadProgress, onDownloadProgress } })
  }
}

const handleCheckFileCondition = (file: File | undefined): boolean => {
  if (!file) return false
  const accept = checkAcceptFile(file)

  if (!accept) {
    setErrors('File type is not supported')

    return false
  }

  const maxSize = checkMaxSize(file)

  if (!maxSize) {
    setErrors('File size is too large')

    return false
  }

  setErrors('')

  return true
}

const checkAcceptFile = (file: File): boolean => {
  const fileType = file.type.split('/')[1]

  return acceptFile.value ? acceptFile.value.includes(fileType) : true
}

const checkMaxSize = (file: File): boolean => {
  if (acceptFileSize.value) {
    return file.size / 1000 <= acceptFileSize.value
  }

  return true
}

const onUploadProgress = (progressEvent: ProgressEvent) => {
  percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.8
}

const onDownloadProgress = (progressEvent: ProgressEvent) => {
  if (progressEvent.total === 0) {
    percent.value = 100

    return
  }

  percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.2 + 80
}
</script>
