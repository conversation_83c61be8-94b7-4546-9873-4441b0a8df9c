<template>
  <div>
    <PageHeader :title="routes.agentBanks.name" />
    <Tab v-slot="{ option }" :options="tabOptions">
      <Loader :is-loading="bank.findStatus.isLoading">
        <General v-if="option.value === 'general'" :item="bank.findItem" />
        <Auto v-if="option.value === 'auto'" :item="bank.findItem" />
        <Transfer v-if="option.value === 'transfer'" :item="bank.findItem" />
      </Loader>
    </Tab>
  </div>
</template>
<script lang="tsx" setup>
import { computed, useAgentBankPageLoader } from '#imports'
import { ITabOptions } from '~/components/Tab/types'
import General from '~/features/agent/AgentBankSingle/General.vue'
import Auto from '~/features/agent/AgentBankSingle/Auto.vue'
import Transfer from '~/features/agent/AgentBankSingle/Transfer.vue'

const bank = useAgentBankPageLoader()

const tabOptions = computed<ITabOptions>(() => ({
  tabs: [
    {
      label: 'ข้อมูลบัญชี',
      value: 'general',
      icon: 'user-solid',
    },
    // {
    //   label: 'ตั้งค่าระบบออโต้',
    //   value: 'auto',
    //   icon: 'user-card-solid',
    // },
    // {
    //   label: 'ตั้งค่าระบบโยกเงิน',
    //   value: 'transfer',
    //   icon: 'user-circle-solid',
    // },
  ],
}))
</script>
