<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentCreditReports />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentCreditReports from '~/features/AgentCreditReports/index.vue'

const app = useApp()

definePageMeta({
  middleware: [MIDDLEWARES.AUTH_ADMIN],
})

app.updateDocMeta({ title: routes.agentCreditReports.name })
app.updatePageMeta({
  title: routes.agentCreditReports.name,
  breadcrumbs: [routes.agentCreditReports],
})
</script>
