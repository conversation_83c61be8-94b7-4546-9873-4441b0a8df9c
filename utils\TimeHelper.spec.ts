import { describe, test, expect } from 'vitest'
import { TimeHelper } from '~/utils/TimeHelper'

describe('TimeHelper', () => {
  test('toUTC', () => {
    const date = '2019-10-18 10:32:50'

    expect(TimeHelper.toUTC(date)).toBe('2019-10-18 03:32:50')
    expect(TimeHelper.toUTC('miss')).toBe('miss')
  })

  test('toLocal', () => {
    const date = '2019-10-18 03:32:50'

    expect(TimeHelper.toLocal(date)).toBe('2019-10-18 10:32:50')
    expect(TimeHelper.toLocal('miss')).toBe('miss')
  })

  test('getDateFormTimeWithLocal', () => {
    const date = '2019-10-18 03:32:50'

    expect(TimeHelper.getDateFormTimeWithLocal(date)).toBe('2019-10-18')
    const date2 = '2019-10-18 23:32:50'

    expect(TimeHelper.getDateFormTimeWithLocal(date2)).toBe('2019-10-19')
    expect(TimeHelper.getDateFormTimeWithLocal('miss')).toBe('miss')
  })

  test('getISODateTimeFormTime', () => {
    const date = '2019-10-18 03:32:50'

    expect(TimeHelper.getISODateTimeFormTime(date)).toBe('2019-10-17T20:32:50.000Z')
    expect(TimeHelper.getISODateTimeFormTime('miss')).toBe('miss')
  })

  test('getISODateTimeFormTime', () => {
    const date = '2019-10-18 03:32:50'

    expect(TimeHelper.getDateTimeFormTime(date)).toBe('2019-10-18 03:32:50')

    const date2 = '2019-10-17T20:32:50.000Z'

    expect(TimeHelper.getDateTimeFormTime(date2)).toBe('2019-10-18 03:32:50')
    expect(TimeHelper.getDateTimeFormTime('miss')).toBe('miss')
  })

  test('getTimeFormTime', () => {
    const date = '2019-10-18 03:32:50'

    expect(TimeHelper.getTimeFormTime(date)).toBe('03:32')
    expect(TimeHelper.getTimeFormTime('miss')).toBe('miss')
  })
})
