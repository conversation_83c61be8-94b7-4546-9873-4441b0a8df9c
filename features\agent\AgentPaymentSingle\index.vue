<template>
  <div>
    <PageHeader :title="routes.agentBanks.name" />
    <Form @submit="onSubmit">
      <div class="card p-6">
        <FormFields :options="formFields" />
      </div>
      <div class="flex justify-end space-x-3 mt-6">
        <Button class="btn-secondary btn-min" @click="router.back()"> ยกเลิก</Button>
        <Button type="submit" class="btn-primary btn-min"> อัพเดต</Button>
      </div>
    </Form>
  </div>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields, useAgentPaymentPageLoader, useDialog, useRoute } from '#imports'
import { INPUT_TYPES } from '~/components/Form/types'
import { IPaymentItem } from '~/models/payment'
import { paymentTypeOptions } from '~/constants/paymentTypes'

const dialog = useDialog()
const route = useRoute()
const router = useRouter()
const payment = useAgentPaymentPageLoader()

const form = useForm<Partial<IPaymentItem>>()

onMounted(async () => {
  form.setValues(payment.findItem!)
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ประเภทการชำระเงิน',
      name: 'type',
      placeholder: 'กรอกประเภทการชำระเงิน',
      rules: yup.string().required(),
      options: paymentTypeOptions,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'name',
      placeholder: 'กรอกชื่อวิธีการชำระเงิน',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Merchant Code',
      name: 'settings.merchant_code',
      placeholder: 'กรอกรหัสร้านค้า',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'API Key',
      name: 'settings.api_key',
      placeholder: 'กรอก API key',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ฝากเงินขั้นต่ำ',
      name: 'settings.min_deposit',
      placeholder: 'กรอกฝากขั้นต่ำ',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ฝากเงินสูงสุด',
      name: 'settings.max_deposit',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ถอนเงินขั้นต่ำ',
      name: 'settings.min_withdraw',
      placeholder: 'กรอกถอนขั้นต่ำ',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ถอนเงินสูงสุด',
      name: 'settings.max_withdraw',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'ฝากเงินอัตโนมัติ',
      name: 'is_deposit_auto',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'ถอนเงินอัตโนมัติ',
      name: 'is_withdraw_auto',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ลำดับการแสดงผล',
      name: 'sort_order',
      placeholder: 'กรอกลำดับการแสดงผล',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'เปิดใช้งาน',
      name: 'is_enabled',
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  payment.update(route.params.id as string, {
    ...data,
    settings: {
      ...data.settings,
      min_deposit: Number(data.settings?.min_deposit),
      max_deposit: Number(data.settings?.max_deposit),
      min_withdraw: Number(data.settings?.min_withdraw),
      max_withdraw: Number(data.settings?.max_withdraw),
    },
    sort_order: Number(data.sort_order),
  })
})

useWatchTrue(
  () => payment.updateStatus.isSuccess,
  () => {
    dialog.success({
      title: 'สำเร็จ',
      message: 'แก้ไข Payment สำเร็จ',
    })
  }
)

useWatchTrue(
  () => payment.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(payment.updateStatus.errorData),
    })
  }
)
</script>
