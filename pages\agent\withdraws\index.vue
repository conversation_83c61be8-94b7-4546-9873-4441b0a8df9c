<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentWithdraws />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentWithdraws from '~/features/agent/AgentWithdraws/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentWithdraws.name })
app.updatePageMeta({
  title: routes.agentWithdraws.name,
  breadcrumbs: [routes.agentWithdraws],
})
</script>
