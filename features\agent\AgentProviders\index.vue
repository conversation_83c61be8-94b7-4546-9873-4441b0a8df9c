<template>
  <div>
    <div class="flex items-center justify-between">
      <PageHeader title="ค่ายเกม" />
    </div>
    <Table :options="providerTableOptions" @pageChange="provider.fetch" @search="provider.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { IAgentProviderItem } from '~/models/provider'
import ProvidersColumnAction from '~/features/AgentSingle/ProvidersColumnAction.vue'

const provider = useAgentProviderPageLoader()

provider.setFetchLoading()
onMounted(() => {
  provider.fetch()
})

const providerTableOptions = useTable<IAgentProviderItem>({
  repo: provider,
  columns: () => [
    {
      value: 'รูป',
    },
    {
      value: 'ชื่อ',
    },
    {
      value: 'เปอเซนต์',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.provider.name,
        },
        {
          value: item.percent,
        },
        {
          value: item.is_enabled,
          type: COLUMN_TYPES.BOOLEAN,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ProvidersColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            provider: item,
          },
          on: {
            reload: () => {
              provider.fetch()
            },
          },
        },
      ]
    }),
})
</script>
