<template>
  <Table :options="tableOptions" @pageChange="transaction.fetch" @search="transaction.search" />
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { useAgentGameTransactionPageLoader } from '~/loaders/agentGame'
import { IGameTransactionItem } from '~/models/game'
import Game from '~/containers/AgentGameTransactionTable/Game.vue'

const props = defineProps<{
  transaction: useAgentGameTransactionPageLoader
}>()

props.transaction.setFetchLoading()
onMounted(() => {
  props.transaction.fetch()
})

const tableOptions = useTable<IGameTransactionItem>({
  options: {
    isNotChangeRoute: true,
  },
  repo: props.transaction,
  columns: () => [
    {
      value: 'เลขธุรกรรม',
    },
    {
      value: 'ผู้ใช้',
    },
    {
      value: 'ผู้ให้บริการ',
    },
    {
      value: 'ชื่อเกม',
    },
    {
      value: 'ยอดก่อนเดิมพัน',
      className: 'text-right',
    },
    {
      value: 'ยอดเดิมพัน',
      className: 'text-right',
    },
    {
      value: 'ยอดหลังเดิมพัน',
      className: 'text-right',
    },
    {
      value: 'เงินรางวัล',
      className: 'text-right',
    },
    {
      value: 'ซื้อฟรีสปิน',
      className: '',
    },
    {
      value: 'ได้รับรอบหมุนฟรี',
      className: '',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'วันที่',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.tx_id,
        },
        {
          value: item.player,
          type: COLUMN_TYPES.PLAYER,
        },
        {
          value: item.provider.name,
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          props: {
            game: item.game,
          },
          value: Game,
        },
        {
          value: item.before_credit,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.bet_amount,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.after_credit,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.win_amount,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.is_feature
            ? '<div class="px-3 py-1 text-white bg-green-500 rounded-full max-w-fit">รับแล้ว</div>'
            : '<div class="px-3 py-1 text-white bg-red-500 rounded-full max-w-fit">ไม่ได้รับ</div>',
        },
        {
          value: item.is_free_round
            ? '<div class="px-3 py-1 text-white bg-green-500 rounded-full max-w-fit">รับแล้ว</div>'
            : '<div class="px-3 py-1 text-white bg-red-500 rounded-full max-w-fit">ไม่ได้รับ</div>',
        },
        {
          value: item.status,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
      ]
    }),
})
</script>
