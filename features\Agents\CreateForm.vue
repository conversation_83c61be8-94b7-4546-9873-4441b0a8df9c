<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const form = useForm<
  Partial<{
    name: string
    prefix: string
    domain: string
    credit: string
    remark?: string
  }>
>()

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'name',
      rules: yup.string().min(3).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Prefix',
      name: 'prefix',
      rules: yup.string().noSpaces().english().lowercase().min(2).max(5).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'โดเมน',
      name: 'domain',
      rules: yup.string().url().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เครดิต',
      name: 'credit',
      rules: yup.number().min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      label: 'หมายเหตุ',
      name: 'remark',
      rules: yup.string(),
      rows: 5,
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    credit: Number(data.credit),
  })
})
</script>
