<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IPaymentItem } from '~/models/payment'
import { paymentTypeOptions } from '~/constants/paymentTypes'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const form = useForm<Partial<IPaymentItem>>({
  initialValues: {
    type: 'vpay',
    name: '',
    settings: {
      merchant_code: '',
      api_key: '',
      min_deposit: 0,
      max_deposit: 0,
      min_withdraw: 0,
      max_withdraw: 0,
    },
    sort_order: 0,
    is_deposit_auto: true,
    is_withdraw_auto: true,
    is_enabled: true,
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ประเภทการชำระเงิน',
      name: 'type',
      placeholder: 'กรอกประเภทการชำระเงิน',
      rules: yup.string().required(),
      options: paymentTypeOptions,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'name',
      placeholder: 'กรอกชื่อวิธีการชำระเงิน',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Merchant Code',
      name: 'settings.merchant_code',
      placeholder: 'กรอกรหัสร้านค้า',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'API Key',
      name: 'settings.api_key',
      placeholder: 'กรอก API key',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ฝากเงินขั้นต่ำ',
      name: 'settings.min_deposit',
      placeholder: 'กรอกฝากขั้นต่ำ',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ฝากเงินสูงสุด',
      name: 'settings.max_deposit',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ถอนเงินขั้นต่ำ',
      name: 'settings.min_withdraw',
      placeholder: 'กรอกถอนขั้นต่ำ',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ถอนเงินสูงสุด',
      name: 'settings.max_withdraw',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ลำดับการแสดงผล',
      name: 'sort_order',
      placeholder: 'กรอกลำดับการแสดงผล',
      rules: yup.number().integer().min(0).required(),
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'ฝากเงินอัตโนมัติ',
      name: 'is_deposit_auto',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'ถอนเงินอัตโนมัติ',
      name: 'is_withdraw_auto',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'เปิดใช้งาน',
      name: 'is_enabled',
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    settings: {
      ...data.settings,
      min_deposit: Number(data.settings?.min_deposit),
      max_deposit: Number(data.settings?.max_deposit),
      min_withdraw: Number(data.settings?.min_withdraw),
      max_withdraw: Number(data.settings?.max_withdraw),
    },
    sort_order: Number(data.sort_order),
  })
})
</script>
