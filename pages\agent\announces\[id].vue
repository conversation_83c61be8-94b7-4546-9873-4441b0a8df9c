<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentBankSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta, useAgentAnnouncePageLoader } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentBankSingle from '~/features/agent/AgentBankSingle/index.vue'

const app = useApp()
const route = useRoute()
const announce = useAgentAnnouncePageLoader()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_AGENT,
    async (to) => {
      const announce = useAgentAnnouncePageLoader()

      await announce.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(announce.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.agentAnnounceEdit(
  route.params.id as string,
  announce.findItem?.value?.account_name
)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.agentAnnounce, nav],
})
</script>
