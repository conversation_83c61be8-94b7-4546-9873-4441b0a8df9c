import { useObjectLoader } from '~/lib/api/loaderObject'
import { type IAgentStatItem } from '~/models/agentStat'
import { useRequestOptions } from '~/hooks/useRequestOptions'

export const useAgentStatsLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentStatItem>({
    method: 'get',
    url: '/agent/stats',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
