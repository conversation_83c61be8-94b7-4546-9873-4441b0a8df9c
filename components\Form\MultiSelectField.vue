<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      :class="[
        fieldClassName,
        'pb-0',
        {
          disabled: disabled,
        },
      ]"
    >
      <VueMultiselect
        v-model="innerValue"
        :disabled="disabled"
        :name="name"
        :options="options"
        :multiple="true"
        :taggable="true"
        :close-on-select="false"
        :tag-placeholder="placeholder ?? props.label"
        :placeholder="placeholder ?? props.label"
        label="label"
        track-by="value"
      />
    </div>
  </FieldWrapper>
</template>
<script lang="ts" setup>
import VueMultiselect from 'vue-multiselect'
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { IMultiSelectFieldProps } from '~/components/Form/multiselect_field.types'
import { ref } from '#imports'
import { IOption } from '~/components/Form/types'

const props = withDefaults(defineProps<IMultiSelectFieldProps>(), {})
const emits = defineEmits<{
  (event: 'change', value: IOption[]): void
}>()

const innerValue = ref<IOption[]>([])
const { value, wrapperProps, fieldClassName, disabled, handleChange } =
  useFieldHOC<IOption[]>(props)

watch(
  () => value.value,
  (n) => {
    for (let i = 0; i < n.length; i++) {
      const index = props.options.findIndex((o) => o.value === n[i])

      if (index !== -1) {
        innerValue.value = [...innerValue.value, props.options[index]]
      }
    }
  }
)

watch(
  () => innerValue.value,
  (n) => {
    handleChange(n)
  },
  { deep: true }
)
</script>
