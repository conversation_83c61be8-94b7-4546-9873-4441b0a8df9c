<template>
  <Table :options="tableOptions" @pageChange="transaction.fetch" @search="transaction.search" />
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { useAgentGameSummaryPageLoader } from '~/loaders/agentGame'
import { IGameSummaryItem } from '~/models/game'

const props = defineProps<{
  transaction: useAgentGameSummaryPageLoader
}>()

props.transaction.setFetchLoading()
onMounted(() => {
  props.transaction.fetch()
})

const tableOptions = useTable<IGameSummaryItem>({
  options: {
    isNotChangeRoute: true,
  },
  repo: props.transaction,
  columns: () => [
    {
      value: 'รูป',
    },
    {
      value: 'ผู้ให้บริการ',
    },
    {
      value: 'ยอดแทง',
      className: 'text-right',
    },
    {
      value: 'ยอดถูก',
      className: 'text-right',
    },
    {
      value: 'ส่วนต่าง',
      className: 'text-right',
    },
    {
      value: 'ค่าส่วนแบ่งผู้ให้บริการ',
      className: 'text-right',
    },
    {
      value: 'กำไร',
      className: 'text-right',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.provider.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.provider.name,
        },
        {
          value: item.total_bet,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.total_win,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.total_diff,
          type: COLUMN_TYPES.NUMBER,
          className: item.total_diff >= 0 ? 'text-success text-right' : 'text-danger text-right',
        },
        {
          value: item.total_provider_share,
          type: COLUMN_TYPES.NUMBER,
          className:
            item.total_provider_share >= 0 ? 'text-success text-right' : 'text-danger text-right',
        },
        {
          value: item.total_profit,
          type: COLUMN_TYPES.NUMBER,
          className: item.total_profit >= 0 ? 'text-success text-right' : 'text-danger text-right',
        },
      ]
    }),
})
</script>
