<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <GameConnectionSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta, useGameConnectionPageStore } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import GameConnectionSingle from '~/features/GameConnectionSingle/index.vue'

const app = useApp()
const route = useRoute()
const game = useGameConnectionPageLoader()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_ADMIN,
    async (to) => {
      const game = useGameConnectionPageStore()

      await game.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(game.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.connectionEdit(route.params.id as string, game.findItem?.name)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.connections, nav],
})
</script>
