<template>
  <div>
    <PageHeader :title="routes.agentBankGroups.name" />
    <Table :options="tableOptions" @pageChange="group.fetch" @search="group.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { useAgentBankGroupPageLoader } from '~/loaders/agentBankGroup'
import { IBankGroupItem } from '~/models/bankGroup'
import ColumnAction from '~/features/agent/AgentBankGroups/ColumnAction.vue'
import { COLUMN_TYPES } from '~/components/Table/types'
import BankList from '~/features/agent/AgentBankGroups/BankList.vue'

const group = useAgentBankGroupPageLoader()

group.setFetchLoading()
onMounted(() => {
  group.fetch()
})

const tableOptions = useTable<IBankGroupItem>({
  repo: group,
  columns: () => [
    {
      className: 'text-center',
      value: 'ชื่อกลุ่ม',
    },
    {
      value: 'บัญชีหลัก',
    },
    {
      value: 'บัญชีรอง',
    },
    {
      value: 'สร้างวันที่',
    },
    {
      value: 'กระทำ',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.name,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.default_bank && {
            image_url: item.default_bank.bank.image_url,
            name: item.default_bank.account_name,
            number: item.default_bank.account_number,
            bank: item.default_bank.bank.name_th,
          },
          type: item.default_bank ? COLUMN_TYPES.BANK : undefined,
        },
        {
          value: BankList,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            items: item.banks,
          },
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            item,
          },
        },
      ]
    }),
})
</script>
