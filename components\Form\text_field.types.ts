import { type IFieldProps, type IFormFieldBase, type INPUT_TYPES } from '~/components/Form/types'

export interface ITextFieldProps extends IFieldProps {
  type?: 'text' | 'password' | 'email' | 'number'
  step?: number
  prependIcon?: VueComponent
  appendIcon?: VueComponent
}

export type ITextField = IFormFieldBase<
  INPUT_TYPES.TEXT | INPUT_TYPES.PASSWORD,
  ITextFieldProps,
  {
    change?: (value: string) => void
    blur?: (value: string) => void
    mounted?: (value: string) => void
  }
>
