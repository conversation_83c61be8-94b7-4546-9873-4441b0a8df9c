<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentAnnounces />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentAnnounces from '~/features/agent/AgentAnnounces/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentAnnounce.name })
app.updatePageMeta({
  title: routes.agentAnnounce.name,
  breadcrumbs: [routes.agentAnnounce],
})
</script>
