<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentBankSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta, useAgentBankPageLoader } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentBankSingle from '~/features/agent/AgentBankSingle/index.vue'

const app = useApp()
const route = useRoute()
const bank = useAgentBankPageLoader()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_AGENT,
    async (to) => {
      const bank = useAgentBankPageLoader()

      await bank.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(bank.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.agentBankEdit(route.params.id as string, bank.findItem?.account_name)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.agentBanks, nav],
})
</script>
