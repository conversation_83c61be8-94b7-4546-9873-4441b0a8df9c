export interface IProviderItem {
  id: string
  created_at: string
  updated_at: string
  name: string
  short_name: string
  type: string[]
  percent: number
  no: number
  image_url: string
  status: string
  is_enabled: boolean
  config: any
}

export interface IAgentProviderItem {
  id: string
  agent_id: string
  is_enabled: boolean
  percent: number
  no: number
  image_url: string
  status: string
  created_at: string
  updated_at: string
  provider: IProviderItem
}
