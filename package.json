{"private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "e2e": "playwright test --headed", "e2e:debug": "playwright test --debug", "start": "node .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .ts,.vue,.tsx,.js", "lint:style": "stylelint \"**/*.{scss,css}\"", "lint:style:fix": "stylelint \"**/*.{scss,css}\" --fix", "lint:fix": "eslint . --ext .ts,.vue,.tsx,.js --fix", "prepare": "husky install"}, "devDependencies": {"@nuxt/devtools": "^0.6.2", "@nuxt/test-utils-edge": "^3.6.0-28122379.bd34a8c1", "@nuxtjs/eslint-config-typescript": "^12.0.0", "@nuxtjs/tailwindcss": "^6.8.0", "@playwright/test": "^1.35.1", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "@types/faker": "^6.6.9", "@types/node": "^18.11.1", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "@vitest/coverage-c8": "^0.29.3", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^16.0.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unused-imports": "^2.0.0", "eslint-plugin-vue": "^9.14.0", "husky": "^8.0.0", "lint-staged": "^13.0.3", "nuxt": "3.5.3", "postcss-custom-properties": "^13.0.0", "prettier": "^2.8.8", "sass": "^1.57.1", "stylelint": "^15.6.2", "stylelint-config-prettier-scss": "^0.0.1", "stylelint-config-standard-scss": "^6.1.0", "ts-node": "^10.9.1", "typescript": "*", "unplugin-auto-import": "^0.13.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-vue-type-imports": "^0.2.4", "vitest": "^0.31.1"}, "dependencies": {"@cyhnkckali/vue3-color-picker": "^2.0.2", "@faker-js/faker": "^8.0.1", "@headlessui/vue": "^1.7.8", "@heroicons/vue": "^2.1.5", "@kyvg/vue3-notification": "^2.9.1", "@nuxtjs/i18n": "^8.0.0-alpha.9", "@pinia/nuxt": "^0.4.11", "@vuepic/vue-datepicker": "^3.6.5", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.1.2", "axios": "^1.2.6", "cookie-universal": "^2.2.2", "dayjs": "^1.11.7", "lodash": "^4.17.21", "pinia": "^2.1.4", "url-join": "^5.0.0", "vee-validate": "4.8.6", "vue-multiselect": "^3.0.0-beta.1", "vue3-lazyload": "^0.3.6", "vuedraggable": "^4.1.0", "yup": "0.32.11", "yup-password": "^0.2.2", "yup-phone": "^1.3.2"}, "lint-staged": {"*.{ts,vue,tsx,js}": "eslint --fix", "*.{css,scss}": "stylelint --fix", "*.{html,json}": "prettier --write"}, "engines": {"node": ">=18.0.0 <19.0.0"}}