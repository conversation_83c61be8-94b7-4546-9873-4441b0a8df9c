export const enum PromotionType {
  NORMAL = 'normal',
  HAPPY_TIME = 'happy_time',
  ONE_TIME = 'one_time',
  ONCE_PER_DAY = 'once_per_day',
  WELCOME_BACK = 'welcome_back',
  FIRST_TIME = 'first_time',
  STACK_TOP_UP = 'stack_top_up',
  FIRST_A_DAY = 'first_a_day',
}

export const enum PromotionSubType {
  ONE_TIME = 'one_time',
  WELCOME_BACK = 'welcome_back',
  SEQUENCE = 'sequence',
}

export const enum BonusType {
  PERCENT = 'percent',
  BONUS = 'bonus',
}

export const enum UserTypeReceiver {
  ALL = 'all',
  BASIC = 'basic',
  VIP = 'vip',
}

export const enum DueDateType {
  NONE = 'none',
  PROMO_CREATED = 'promo_created',
  USER_CREATED = 'user_created',
}

export const enum RedeemByType {
  AUTO = 'auto',
  AGENT = 'agent',
}

export const enum WithdrawStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export const PROMOTION_TYPE_DISPLAY: Record<PromotionType, string> = {
  [PromotionType.NORMAL]: 'โปรโมชั่นหลัก',
  [PromotionType.HAPPY_TIME]: 'แฮปปี้ไทม์',
  [PromotionType.ONE_TIME]: 'โปรโมชั่นใช้ครั้งเดียว',
  [PromotionType.ONCE_PER_DAY]: 'โปรโมชั่นรับครั้งเดียวต่อวัน',
  [PromotionType.WELCOME_BACK]: 'เวลคัมแบ็ค',
  [PromotionType.FIRST_TIME]: 'โปรโมชั่นใช้ครั้งแรก',
  [PromotionType.STACK_TOP_UP]: 'เติมเงินตามจำนวนครั้ง',
  [PromotionType.FIRST_A_DAY]: 'โปรโมชั่นครั้งแรกของวัน',
  [PromotionType.DEPOSIT_CONTINUE]: 'ฝากประจำ',
}

export const BONUS_TYPE_DISPLAY: Record<BonusType, string> = {
  [BonusType.PERCENT]: 'เปอร์เซ็นต์',
  [BonusType.BONUS]: 'โบนัส',
}

export const USER_TYPE_RECEIVER_DISPLAY: Record<UserTypeReceiver, string> = {
  [UserTypeReceiver.ALL]: 'ทั้งหมด',
  [UserTypeReceiver.BASIC]: 'ทั่วไป',
  [UserTypeReceiver.VIP]: 'VIP',
}

export const promotionTypeOptions: IOption[] = [
  {
    value: PromotionType.NORMAL,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.NORMAL],
  },
  {
    value: PromotionType.HAPPY_TIME,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.HAPPY_TIME],
  },
  {
    value: PromotionType.ONE_TIME,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.ONE_TIME],
  },
  {
    value: PromotionType.ONCE_PER_DAY,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.ONCE_PER_DAY],
  },
  {
    value: PromotionType.WELCOME_BACK,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.WELCOME_BACK],
  },
  {
    value: PromotionType.FIRST_TIME,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.FIRST_TIME],
  },
  {
    value: PromotionType.STACK_TOP_UP,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.STACK_TOP_UP],
  },
  {
    value: PromotionType.FIRST_A_DAY,
    label: PROMOTION_TYPE_DISPLAY[PromotionType.FIRST_A_DAY],
  },
]

export const bonusTypeOptions: IOption[] = [
  {
    value: BonusType.PERCENT,
    label: BONUS_TYPE_DISPLAY[BonusType.PERCENT],
  },
  {
    value: BonusType.BONUS,
    label: BONUS_TYPE_DISPLAY[BonusType.BONUS],
  },
]

export const promotionSubTypeOptions: IOption[] = [
  {
    value: PromotionSubType.ONE_TIME,
    label: 'วันละครั้ง',
  },
  {
    value: PromotionSubType.WELCOME_BACK,
    label: 'เวลคัมแบ็ค',
  },
  {
    value: PromotionSubType.SEQUENCE,
    label: 'คำนวนรายบิล',
  },
]

export const userTypeReceiverOptions: IOption[] = [
  {
    value: UserTypeReceiver.ALL,
    label: USER_TYPE_RECEIVER_DISPLAY[UserTypeReceiver.ALL],
  },
  {
    value: UserTypeReceiver.BASIC,
    label: USER_TYPE_RECEIVER_DISPLAY[UserTypeReceiver.BASIC],
  },
  {
    value: UserTypeReceiver.VIP,
    label: USER_TYPE_RECEIVER_DISPLAY[UserTypeReceiver.VIP],
  },
]

export const redeemByTypeOptions: IOption[] = [
  {
    value: RedeemByType.AUTO,
    label: 'เติมอัตโนมัติ',
  },
  {
    value: RedeemByType.AGENT,
    label: 'เติมโดยพนักงาน',
  },
]

export const withdrawStatusOptions: IOption[] = [
  {
    value: WithdrawStatus.ACTIVE,
    label: 'เปิดใช้งาน',
  },
  {
    value: WithdrawStatus.INACTIVE,
    label: 'ปิดใช้งาน',
  },
]

export const dueDateTypeOptions: IOption[] = [
  {
    value: DueDateType.NONE,
    label: 'ไม่มีกำหนด',
  },
  {
    value: DueDateType.PROMO_CREATED,
    label: 'ตามวันที่สร้างโปรโมชั่น',
  },
  {
    value: DueDateType.USER_CREATED,
    label: 'ตามวันที่ลูกค้าสมัครสมาชิก',
  },
]
