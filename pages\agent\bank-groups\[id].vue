<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentBankGroupSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta, useAgentBankGroupPageLoader } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentBankGroupSingle from '~/features/agent/AgentBankGroupSingle/index.vue'

const app = useApp()
const route = useRoute()
const group = useAgentBankGroupPageLoader()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_AGENT,
    async (to) => {
      const group = useAgentBankGroupPageLoader()

      await group.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(group.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.providerEdit(route.params.id as string, group.findItem?.value?.name)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.agentBankGroups, nav],
})
</script>
