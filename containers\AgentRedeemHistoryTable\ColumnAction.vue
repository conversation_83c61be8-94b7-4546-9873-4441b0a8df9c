<template>
  <div class="flex items-center space-x-3">
    <Button
      v-if="item.props?.item.status === 'waiting'"
      class="btn-success btn-sm"
      icon="check-circle-solid"
      :is-only-icon="true"
      :is-loading="approve.status.value.isLoading"
      @click="onApprove"
    />
    <Button
      v-if="item.props?.item.status === 'waiting'"
      class="btn-danger btn-sm"
      icon="x-circle-solid"
      :is-only-icon="true"
      :is-loading="reject.status.value.isLoading"
      @click="onReject"
    />
  </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue'
import { IRowItem } from '~/components/Table/types'
import type { IRedeemHistoryItem } from '~/models/earnPoint'
import {
  useAgentRedeemHistoryRejectLoader,
  useAgentRedeemHistoryApproveLoader,
} from '~/loaders/agentEarnpoint'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ item: IRedeemHistoryItem; onDone: () => void }>>,
    required: true,
  },
})

const approve = useAgentRedeemHistoryApproveLoader()
const reject = useAgentRedeemHistoryRejectLoader()
const dialog = useDialog()

const onApprove = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการอนุมัติการแลกเปลี่ยนนี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      approve.run({
        id: props.item.props!.item!.id,
      })
    })
}

const onReject = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการปฏิเสธการแลกเปลี่ยนนี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      reject.run({
        id: props.item.props!.item!.id,
      })
    })
}

useWatchTrue(
  () => approve.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'อนุมัติการแลกเปลี่ยนสำเร็จ',
      })
      .then(() => {
        props.item.props!.onDone()
      })
  }
)

useWatchTrue(
  () => reject.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ปฏิเสธการแลกเปลี่ยนสำเร็จ',
      })
      .then(() => {
        props.item.props!.onDone()
      })
  }
)
</script>
