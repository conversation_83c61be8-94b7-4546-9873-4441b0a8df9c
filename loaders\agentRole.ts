import { type AxiosResponseHeaders } from 'axios'
import { usePageLoader } from '~/lib/api/loaderPage'
import { useListLoader } from '~/lib/api/loaderList'
import { type IPositionItem } from '~/models/position'
import { type IPermissionItem } from '~/models/permission'
import { useRequestOptions } from '~/hooks/useRequestOptions'

export const useAgentPositionPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IPositionItem>({
    baseURL: '/agent/user-positions',
    getBaseRequestOptions: getDefaultWithAuth,
    fetch: {
      getRequestOptions: () => {
        return {
          ...getDefaultWithAuth(),
          transformResponse: (data: any, _headers: AxiosResponseHeaders, status?: number) => {
            const response = JSON.parse(data)

            if (Number(status) === 200) {
              return {
                page: 1,
                limit: 1000,
                total: response.length,
                count: response.length,
                items: response,
              }
            }

            return response
          },
        }
      },
    },
  })
}

export const useAdminAgentPositionPageLoader = (agenId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IPositionItem>({
    baseURL: `/admin/agents/${agenId}/user-positions`,
    getBaseRequestOptions: getDefaultWithAuth,
    fetch: {
      getRequestOptions: getDefaultWithAuth,
    },
  })
}

export const useAgentPermissionListLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useListLoader<IPermissionItem>({
    url: '/agent/permissions',
    getRequestOptions: getDefaultWithAuth,
  })
}
