import { defineStore } from 'pinia'
import { useListLoader } from '~/lib/api/loaderList'
import { useObjectLoader } from '~/lib/api/loaderObject'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IPromotionItem, type IPromotionRedeemItem } from '~/models/promotion'

export const useAgentPromotionPageLoader = defineStore('agent.promotion.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IPromotionItem>({
    baseURL: '/agent/promotions',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentPromotionAllLoader = defineStore('agent.promotion.all', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useListLoader<IPromotionItem>({
    url: '/agent/promotions/all',
    getRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentPromotionSortLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<{ promotion_ids: string[] }>({
    method: 'POST',
    url: '/agent/promotions/sort',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentPromotionRedeemHistoryPageLoader = defineStore(
  'agent.promotion_redeem_history_player.page',
  () => {
    const { getDefaultWithAuth } = useRequestOptions()
    const route = useRoute()

    return usePageLoader<IPromotionRedeemItem>({
      baseURL: `/agent/promotion-redeems?player_id=${route.params.id}`,
      getBaseRequestOptions: () => getDefaultWithAuth(),
    })
  }
)

export const useAgentPromotionRedeemHistoryDeleteLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IPromotionRedeemItem>({
    url: `/agent/promotion-redeems/${userId}`,
    method: 'DELETE',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentPromotionRedeemHistoryAddLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IPromotionRedeemItem>({
    url: `/agent/promotion-redeems/${userId}`,
    method: 'POST',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
