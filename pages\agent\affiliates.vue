<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentAffiliates />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentAffiliates from '~/features/agent/AgentAffiliates/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentAffiliates.name })
app.updatePageMeta({
  title: routes.agentAffiliates.name,
  breadcrumbs: [routes.agentAffiliates],
})
</script>
