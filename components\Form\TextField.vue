<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      :class="[
        fieldClassName,
        'flex flex-row py-[1px] px-[1px]',
        {
          disabled: disabled,
        },
      ]"
    >
      <div v-if="prependIcon" class="prepend-icon">
        <component :is="prependIcon" />
      </div>
      <input
        :class="[fieldClassName, 'bg-transparent ring-0 w-full']"
        :disabled="disabled"
        :name="name"
        :placeholder="placeholder ?? props.label"
        :type="isShowPassword ? 'text' : props.type || 'text'"
        :value="value"
        :step="step"
        :autofocus="autoFocus"
        @input="handleChange"
        @blur="handleBlur"
      />
      <div v-if="type === 'password'" class="append-icon cursor-pointer">
        <i
          :class="[
            'ic text-gray-300 hover:text-gray',
            !isShowPassword ? 'ic-eye-slash-solid' : 'ic-eye-solid',
          ]"
          @click="isShowPassword = !isShowPassword"
        />
      </div>
      <div v-if="appendIcon" class="append-icon">
        <component :is="appendIcon" />
      </div>
    </div>
  </FieldWrapper>
</template>
<script lang="ts" setup>
import { useFieldHOC } from '~/hooks/useForm'
import { ref } from '#imports'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { ITextFieldProps } from '~/components/Form/text_field.types'

const props = withDefaults(defineProps<ITextFieldProps>(), {})
const emits = defineEmits<{
  (event: 'change', value: string): void
  (event: 'blur', value: string): void
  (event: 'mounted', value: string): void
}>()

const { value, wrapperProps, onChange, fieldClassName, disabled } = useFieldHOC<string>(props)
const isShowPassword = ref(false)

onMounted(() => {
  emits('mounted', value.value)
})

const handleChange = (e: Event) => {
  onChange(e as InputEvent)
  const target = e.target as HTMLInputElement

  emits('change', target?.value)
}

const handleBlur = (e: FocusEvent) => {
  const target = e.target as HTMLInputElement

  emits('blur', target?.value)
}
</script>
