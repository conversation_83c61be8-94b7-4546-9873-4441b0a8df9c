<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentGameTransactions />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentGameTransactions from '~/features/agent/AgentGameTransactions/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentGameTransactions.name })
app.updatePageMeta({
  title: routes.agentGameTransactions.name,
  breadcrumbs: [routes.agentGameTransactions],
})
</script>
