<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentPayments />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentPayments from '~/features/agent/AgentPayments/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentPayments.name })
app.updatePageMeta({
  title: routes.agentPayments.name,
  breadcrumbs: [routes.agentPayments],
})
</script>
