<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentPaymentSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta, useAgentPaymentPageLoader } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentPaymentSingle from '~/features/agent/AgentPaymentSingle/index.vue'

const app = useApp()
const route = useRoute()
const payment = useAgentPaymentPageLoader()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_AGENT,
    async (to) => {
      const bank = useAgentPaymentPageLoader()

      await bank.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(bank.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.agentPaymentEdit(route.params.id as string, payment.findItem?.name)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.agentPayments, nav],
})
</script>
