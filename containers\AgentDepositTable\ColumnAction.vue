<template>
  <div v-if="item.props?.item.status === 'pending'" class="flex items-center space-x-3">
    <Modal v-model="isShowModal" title="ปฏิเสธการฝากเงิน" class="modal-sm">
      <Form @submit="onRejectSubmit">
        <FormFields :options="fields" :form="form" />
        <Button class="btn-danger w-full" type="submit" :is-loading="reject.status.value.isLoading">
          Reject
        </Button>
      </Form>
    </Modal>
    <Button
      class="btn-success btn-sm"
      icon="check-circle-solid"
      :is-only-icon="true"
      :is-loading="approve.status.value.isLoading"
      @click="onApprove"
    />
    <Button
      class="btn-danger btn-sm"
      icon="x-circle-solid"
      :is-only-icon="true"
      :is-loading="reject.status.value.isLoading"
      @click="isShowModal = true"
    />
  </div>
</template>
<script lang="tsx" setup>
import { PropType } from 'vue'
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { IRowItem } from '~/components/Table/types'
import { IAgentDepositItem } from '~/models/deposit'
import { useAgentDepositApproveLoader, useAgentDepositRejectLoader } from '~/loaders/agentDeposit'
import { INPUT_TYPES } from '~/components/Form/types'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ item: IAgentDepositItem; onDone: () => void }>>,
    required: true,
  },
})

const approve = useAgentDepositApproveLoader(props.item.props!.item.id)
const reject = useAgentDepositRejectLoader(props.item.props!.item.id)
const dialog = useDialog()
const isShowModal = ref(false)
const form = useForm()

const fields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      label: 'หมายเหตุ',
      name: 'remark',
      rows: 5,
      rules: yup.string().required(),
    },
  },
])

const onApprove = () => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการอนุมัติการฝากเงินนี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      approve.run({})
    })
}

const onRejectSubmit = form.handleSubmit((values) => {
  dialog
    .warning({
      title: 'ยืนยัน',
      message: 'คุณต้องการปฏิเสธการฝากเงินนี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      reject.run(values)
    })
})

useWatchTrue(
  () => approve.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'อนุมัติการฝากเงินสำเร็จ',
      })
      .then(() => {
        props.item.props!.onDone()
      })
  }
)

useWatchTrue(
  () => approve.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(approve.status.value.errorData),
    })
  }
)

useWatchTrue(
  () => reject.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ปฏิเสธการฝากเงินสำเร็จ',
      })
      .then(() => {
        props.item.props!.onDone()
      })
  }
)

useWatchTrue(
  () => reject.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(reject.status.value.errorData),
    })
  }
)
</script>
