<template>
  <div>
    <PageHeader :title="routes.agentAffiliates.name" />
    <Loader :is-loading="affiliate.status.value.isLoading">
      <div class="space-y-4 max-w-2xl mx-auto">
        <div class="card p-6">
          <h1 class="text-lg font-bold mb-3">Bet</h1>
          <Form @submit="onBetSubmit">
            <FormFields :form="formBet" :options="formBetFields" />
            <Button
              type="submit"
              class="btn-primary w-full btn-min"
              :is-loading="bet.status.value.isLoading"
            >
              บันทึก
            </Button>
          </Form>
        </div>
        <div class="card p-6">
          <h1 class="text-lg font-bold mb-3">First Deposit</h1>
          <Form @submit="onFirstSubmit">
            <FormFields :form="formFirst" :options="formFirstDepositFields" />
            <Button
              type="submit"
              class="btn-primary w-full btn-min"
              :is-loading="first.status.value.isLoading"
            >
              บันทึก
            </Button>
          </Form>
        </div>
      </div>
    </Loader>
  </div>
</template>
<script lang="ts" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import {
  IAgentAffiliateSettingItem,
  useAgentAffiliateSettingLoader,
  useAgentAffiliateSettingUpdateLoader,
} from '~/loaders/agentAffiliate'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'

const dialog = useDialog()
const affiliate = useAgentAffiliateSettingLoader()
const first = useAgentAffiliateSettingUpdateLoader('first_deposit')
const bet = useAgentAffiliateSettingUpdateLoader('bet')
const formBet = useForm<Partial<IAgentAffiliateSettingItem['bet']>>()
const formFirst = useForm<Partial<IAgentAffiliateSettingItem['first_deposit']>>()

affiliate.setLoading()

onMounted(() => {
  affiliate.run()
})

useWatchTrue(
  () => affiliate.status.value.isSuccess,
  () => {
    formBet.setValues(affiliate.data.value!.bet)
    formFirst.setValues(affiliate.data.value!.first_deposit)
  }
)

const formBetFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'percentage',
      name: 'percentage',
      rules: yup.number().min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'max level',
      name: 'max_level',
      rules: yup.number().min(1).required(),
    },
  },
])

const formFirstDepositFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'percentage',
      name: 'percentage',
      rules: yup.number().min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'max level',
      name: 'max_level',
      rules: yup.number().min(1).required(),
    },
  },
])

const onBetSubmit = formBet.handleSubmit((data) => {
  bet.run({
    percentage: Number(data.percentage),
    max_level: Number(data.max_level),
  })
})

const onFirstSubmit = formFirst.handleSubmit((data) => {
  first.run({
    percentage: Number(data.percentage),
    max_level: Number(data.max_level),
  })
})

useWatchTrue(
  () => bet.status.value.isSuccess,
  () => {
    dialog.success({
      title: 'สำเร็จ',
      message: 'อัพเดทสำเร็จ',
    })
  }
)

useWatchTrue(
  () => first.status.value.isSuccess,
  () => {
    dialog.success({
      title: 'สำเร็จ',
      message: 'อัพเดทสำเร็จ',
    })
  }
)
</script>
