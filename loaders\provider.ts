import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IProviderItem } from '~/models/provider'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useProviderPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IProviderItem>({
    baseURL: '/admin/providers',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useProviderEnabledUpdate = (
  providerId: string,
  options: {
    isShowDialog?: boolean
  } = {}
) => {
  const { getDefaultWithAuth } = useRequestOptions()
  const dialog = useDialog()
  const provider = useProviderPageStore()

  const loader = useObjectLoader<IProviderItem, { is_enabled: boolean }>({
    method: 'post',
    url: `/admin/providers/${providerId}/enabled`,
    getRequestOptions: () => getDefaultWithAuth(),
  })

  if (options.isShowDialog) {
    useWatchTrue(
      () => loader.status.value.isSuccess,
      () => {
        void dialog
          .success({
            title: 'สำเร็จ',
            message: 'อัพเดทสถานะเรียบร้อยแล้ว',
          })
          .then(() => {
            void provider.find(providerId)
          })
      }
    )

    useWatchTrue(
      () => loader.status.value.isError,
      () => {
        void dialog.error({
          title: 'ผิดพลาด',
          message: 'อัพเดทสถานะล้มเหลว',
        })
      }
    )
  }

  return loader
}

export const useProviderPageStore = defineStore('useProviderPage', useProviderPageLoader)
