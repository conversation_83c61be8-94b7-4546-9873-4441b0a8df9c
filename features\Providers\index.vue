<template>
  <div>
    <PageHeader :title="routes.providers.name" />
    <div class="flex justify-end">
      <Modal
        v-model="isShowCreateModal"
        class="modal-md"
        title="สร้างค่ายเกม"
        :no-backdrop-close="true"
      >
        <p>create</p>
      </Modal>
    </div>
    <Table :options="tableOptions" @pageChange="provider.fetch" @search="provider.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { useProviderPageLoader } from '~/loaders/provider'
import { IProviderItem } from '~/models/provider'
import ColumnAction from '~/features/Providers/ColumnAction.vue'

const provider = useProviderPageLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)

provider.setFetchLoading()
onMounted(() => {
  provider.fetch()
})

useWatchTrue(
  () => provider.addStatus.value.isSuccess,
  () => {
    isShowCreateModal.value = false
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างค่ายเกมสำเร็จ',
      })
      .then(() => {
        provider.fetch()
      })
  }
)

useWatchTrue(
  () => provider.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(provider.updateStatus.value.errorData),
    })
  }
)

const tableOptions = useTable<IProviderItem>({
  repo: provider,
  columns: () => [
    {
      value: 'ลำดับ',
    },
    {
      value: 'รูป',
    },
    {
      value: 'ชื่อ',
    },
    {
      value: 'ประเภท',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.no,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.name,
          type: COLUMN_TYPES.LINK,
          props: {
            href: routes.providerEdit(item.id).to,
          },
        },
        {
          value: item.type.join(', '),
        },
        {
          value: item.is_enabled,
          type: COLUMN_TYPES.BOOLEAN,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            agent: item,
          },
        },
      ]
    }),
})
</script>
