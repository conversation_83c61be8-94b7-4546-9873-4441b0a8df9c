/** @type {import("tailwindcss").Config} */

module.exports = {
  important: true,
  content: [
    'app.vue',
    'error.vue',
    './components/**/*.{js,vue,ts}',
    './features/**/*.{js,vue,ts}',
    './containers/**/*.{js,vue,ts}',
    './layouts/**/*.{js,vue,ts}',
    './pages/**/*.{js,vue,ts}',
    './error.{js,vue,ts}',
    './utils/**/*.{js,vue,ts}',
  ],
  theme: {
    extend: {
      borderWidth: {
        1: '1px',
      },
      fontFamily: {
        display: ['Poppins', 'Prompt', 'sans-serif'],
        poppins: ['Poppins', 'Prompt', 'sans-serif'],
        prompt: ['Prompt', 'sans-serif'],
      },
      borderRadius: {
        10: '10px',
        20: '20px',
        30: '30px',
        40: '40px',
      },
      colors: {
        black: '#20243E',
        white: '#FFFFFF',
        dark: {
          DEFAULT: '#20243E',
          50: '#6D7280',
          100: '#505050',
        },
        light: {
          DEFAULT: '#FAFAFA',
        },
        gray: {
          DEFAULT: '#9095A6',
          disabled: '#C1C4D0',
          border: '#E2E4EA',
          fill: '#F4F5FA',
        },
        primary: {
          DEFAULT: '#3675FB',
          50: '#F8F8FF',
          100: '#E8EFFD',
          200: '#bee3f8',
          300: '#90cdf4',
          400: '#63b3ed',
          500: '#3675FB',
          600: '#0068FE',
          700: '#2b6cb0',
          800: '#2c5282',
          900: '#20243E',
        },
        secondary: {
          DEFAULT: '#EE8B36',
          50: '#fdf1e7',
          100: '#f9d6b8',
          200: '#f5bb89',
          300: '#f1a05a',
          400: '#ed852b',
          500: '#d46b12',
          600: '#a5540e',
          700: '#763c0a',
          800: '#472406',
          900: '#180c02',
        },
        info: {
          DEFAULT: '#F64976',
          50: '#FFEBF0',
          100: '#F87395',
        },
        danger: {
          DEFAULT: '#D0021B',
          50: '#ffe5e5',
          100: '#FF4C4C',
        },
        success: {
          DEFAULT: '#01B460',
          50: '#E2FFF0',
          100: '#52CB8C',
        },
        warning: {
          DEFAULT: '#EE7336',
          50: '#FFEBE2',
          100: '#FF864B',
        },
      },
    },
  },
  plugins: [
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('@tailwindcss/forms'),
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('@tailwindcss/typography'),
  ],
}
