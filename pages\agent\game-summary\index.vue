<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentGameSummary />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentGameSummary from '~/features/agent/AgentGameSummary/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentGameSummary.name })
app.updatePageMeta({
  title: routes.agentGameSummary.name,
  breadcrumbs: [routes.agentGameSummary],
})
</script>
