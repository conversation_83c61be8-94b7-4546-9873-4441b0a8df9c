import { type IUserItem } from '~/models/user'

export interface IArticleCategoryItem {
  id: string
  created_at: string
  updated_at: string
  title: string
  slug: string
  description: string
  image_url: string
}

export interface IArticleItem {
  id: string
  created_at: string
  updated_at: string
  category_slug: string
  title: string
  slug: string
  description: string
  image_url: string
  meta: Record<string, any>
  no: number
  agent_id: string
  link: string
  started_at: string
  ended_at: string
  created_by_id: string
  created_by: IUserItem
  is_enabled: boolean
  category: IArticleCategoryItem
}

export interface IAnnounceItem extends IArticleItem {}
