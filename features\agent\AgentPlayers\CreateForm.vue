<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const bank = useBankListLoader()

const form = useForm<
  Partial<{
    title: string
    first_name: string
    last_name: string
    birthdate: string
    password: string
    password_confirm: string
    register_ref?: string
    recommend: string
    line?: string
  }>
>()

bank.setLoading()
onMounted(() => {
  bank.run()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'tel',
      placeholder: 'เบอร์โทรศัพท์*',
      rules: yup.string().numeric().thaiPhone().required(),
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      name: 'password',
      placeholder: 'กรุณาตั้งรหัสผ่าน',
      label: 'รหัสผ่าน',
      rules: yup.string().min(8).required(),
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      name: 'password_confirm',
      placeholder: 'กรุณายืนยันรหัสผ่าน',
      label: 'ยืนยันรหัสผ่าน',
      rules: yup
        .string()
        .test('not_match_password', 'กรุณากรอกยืนยันรหัสผ่านใหม่ให้ตรงกัน', (value?: string) => {
          return value === form.values.password
        })
        .required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      class: 'mb-3',
      label: 'ธนาคาร',
      name: 'bank_id',
      placeholder: 'เลือกธนาคาร*',
      rules: yup.string().required(),
      isLoading: bank.status.value.isLoading,
      options: ArrayHelper.toOptions(bank.items.value, 'slug', 'name_th'),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'bank_number',
      placeholder: 'เลขบัญชีธนาคาร*',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      class: 'mb-3',
      name: 'title',
      label: 'กรอกข้อมูลส่วนตัว',
      placeholder: 'เลือกคำนำหน้า*',
      rules: yup.string().required(),
      options: [
        ObjectHelper.createOption('นาย', 'นาย'),
        ObjectHelper.createOption('นาง', 'นาง'),
        ObjectHelper.createOption('นางสาว', 'นางสาว'),
      ],
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      class: 'mb-3',
      name: 'first_name',
      placeholder: 'กรอกชื่อจริง*',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'last_name',
      placeholder: 'กรอกนามสกุล*',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันเกิด (ไม่บังคับ)',
      name: 'birthdate',
      placeholder: 'เลือกวันเกิด',
      max_date: new Date(),
    },
  },

  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'รู้จักเราจากช่องทางใด',
      name: 'recommend',
      placeholder: 'กรุณาเลือกช่องทาง*',
      rules: yup.string().required(),
      options: [
        ObjectHelper.createOption('เพื่อนแนะนำ', 'เพื่อนแนะนำ'),
        ObjectHelper.createOption('Facebook', 'Facebook'),
        ObjectHelper.createOption('Instagram', 'Instagram'),
        ObjectHelper.createOption('Line', 'Line'),
        ObjectHelper.createOption('Google', 'Google'),
        ObjectHelper.createOption('อื่นๆ', 'อื่นๆ'),
      ],
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ไลน์ไอดี (ถ้ามี)',
      name: 'line',
      placeholder: 'กรุณากรอกไลน์ไอดี (ถ้ามี)',
      rules: yup.string(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
