<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentPromotion />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentPromotion from '~/features/agent/AgentPromotions/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentPromotions.name })
app.updatePageMeta({
  title: routes.agentPromotions.name,
  breadcrumbs: [routes.agentPromotions],
})
</script>
