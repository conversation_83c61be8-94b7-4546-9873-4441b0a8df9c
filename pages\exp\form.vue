<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <div class="text-center page-heading mb-4 flex justify-center items-center space-x-4">
      <p>Form Usage</p>
    </div>
    <div class="text-center text-description mb-6">Form Live Demo</div>

    <div class="bg-white p-4 w-[600px] mx-auto">
      <Form @submit="onSubmit">
        <FormFields :options="formFields" />
        <Button type="submit"> submit </Button>
        <Button @click="onSet"> set </Button>
        <Button href="https://google.com" target="_blank" type="link"> google </Button>
      </Form>

      <p>{{ form }}</p>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { INPUT_TYPES } from '~/components/Form/types'
import { ref } from '#imports'
import { ObjectHelper } from '~/utils/ObjectHelper'
import { createFormFields } from '~/hooks/useForm'
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

interface IForm {
  email: string
  name: string
  select: string
  address: string
}

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.form.name })
app.updatePageMeta({
  title: styleguideMenu.form.name,
  breadcrumbs: [styleguideMenu.form],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))
const { handleSubmit, setFieldError } = useForm<Partial<IForm>>({
  initialValues: {
    email: '<EMAIL>',
    select: 'ton',
  },
})

const isComponentLabel = ref(true)

const onSet = () => {
  isComponentLabel.value = false
  setFieldError('address', 'kuy error')
}

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: isComponentLabel.value
        ? defineComponent(() => {
            return () => <span class="text-primary">email</span>
          })
        : 'email',
      name: 'email',
      placeholder: 'email',
      rules: yup.string().required().email(),
      transform: (value) => {
        return value.replace(' ', '')
      },
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'name',
      name: 'name',
      placeholder: 'name',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      label: 'address',
      name: 'address',
      placeholder: 'address',
      rules: yup.string(),
      rows: 5,
      max: 7,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'select',
      name: 'select',
      placeholder: 'select',
      rules: yup.string(),
      initOptions: [ObjectHelper.createOption('ton', 'TonTon')],
      options: [
        ObjectHelper.createOption('long', 'Passakon'),
        ObjectHelper.createOption('kwang', 'Kanittha'),
      ],
    },
    on: {
      change(value) {
        window.console.log('change2', value)
      },
      search(value: any) {
        window.console.log('search', value)
      },
    },
  },
  {
    type: INPUT_TYPES.SELECT_AUTO_COMPLETE,
    props: {
      label: 'select auto complete',
      name: 'select_complete',
      placeholder: 'select auto complete',
      rules: yup.string(),
      initOptions: [ObjectHelper.createOption('ton', 'TonTon')],
      options: [
        ObjectHelper.createOption('long', 'Passakon'),
        ObjectHelper.createOption('kwang', 'Kanittha'),
      ],
    },
    on: {
      change(value) {
        window.console.log('change2', value)
      },
      search(value) {
        window.console.log('search', value)
      },
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'date',
      name: 'date',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'toggle',
      name: 'toggle',
    },
  },
  {
    type: INPUT_TYPES.RADIO_BUTTON,
    props: {
      label: 'radio',
      name: 'radio',
      rules: yup.string(),
      fieldClassName: 'grid grid-cols-2 gap-4',
      options: [ObjectHelper.createOption('kwang', 'Kanittha')],
    },
    on: {
      change(value) {
        window.console.log('change2', value)
      },
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_FILE,
    class: 'mb-4',
    props: {
      label: 'Upload File',
      name: 'upload_file',
      selectFileLabel: 'Select File',
    },
  },
])

const onSubmit = handleSubmit((values) => {
  window.console.log('submit', values)
})
</script>
