import { useObjectLoader } from '~/lib/api/loaderObject'

export interface IAgentAffiliateSettingItem {
  bet: {
    percentage: number
    max_level: number
  }
  first_deposit: {
    percentage: number
    max_level: number
  }
}

export const useAgentAffiliateSettingLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentAffiliateSettingItem>({
    method: 'get',
    url: 'agent/affiliate-settings',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

type AgentAffiliateSettingType = 'bet' | 'first_deposit'

export const useAgentAffiliateSettingUpdateLoader = (type: AgentAffiliateSettingType) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentAffiliateSettingItem>({
    method: 'patch',
    url: `agent/affiliate-settings/${type}`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
