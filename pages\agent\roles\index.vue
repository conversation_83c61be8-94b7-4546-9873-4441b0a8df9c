<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentRoles />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentRoles from '~/features/agent/AgentRoles/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentRoles.name })
app.updatePageMeta({
  title: routes.agentRoles.name,
  breadcrumbs: [routes.agentRoles],
})
</script>
