<template>
  <div>
    <PageHeader :title="routes.agentDeposits.name" />
    <div class="flex justify-end">
      <Modal
        v-model="isShowCreateModal"
        class="modal-md"
        title="สร้างรายการฝาก"
        :no-backdrop-close="true"
      >
        <CreateForm :status="deposit.addStatus" @submit="deposit.add" />
      </Modal>
      <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">สร้าง</Button>
    </div>
    <AgentDepositTable :deposit="deposit" />
  </div>
</template>
<script lang="tsx" setup>
import CreateForm from './CreateForm.vue'
import { useAgentDepositPageLoader } from '~/loaders/agentDeposit'
import AgentDepositTable from '~/containers/AgentDepositTable/index.vue'

const isShowCreateModal = ref(false)

const deposit = useAgentDepositPageLoader()
const dialog = useDialog()

useWatchTrue(
  () => deposit.addStatus.isSuccess,
  () => {
    isShowCreateModal.value = false

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างรายการฝากสำเร็จ',
      })
      .then(() => {
        deposit.fetch()
      })
  }
)

useWatchTrue(
  () => deposit.addStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(deposit.addStatus.errorData),
    })
  }
)
</script>
