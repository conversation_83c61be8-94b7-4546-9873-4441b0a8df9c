<template>
  <header class="flex justify-between items-center py-4 px-6 bg-white border-b-4 border-gray-700">
    <div class="flex items-center">
      <button class="text-gray-500 focus:outline-none lg:hidden" @click="setSidebar(true)">
        <svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M4 6H20M4 12H20M4 18H11"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <div class="flex items-center space-x-4">
      <div class="relative">
        <div class="flex items-center space-x-2">
          <p v-if="auth.isAgent">
            เครดิต {{ StringHelper.withComma(auth.me.data?.agent?.credit) }}
          </p>
          <p v-if="auth.isAgent">|</p>

          <div class="relative cursor-pointer font-bold" @click="dropdownOpen = !dropdownOpen">
            <p>Username: {{ auth.me.data?.username }}</p>
          </div>
        </div>

        <div
          v-show="dropdownOpen"
          class="fixed inset-0 h-full w-full z-10"
          style="display: none"
          @click="dropdownOpen = false"
        />

        <div
          v-show="dropdownOpen"
          class="absolute right-0 mt-2 w-48 bg-white rounded-md overflow-hidden shadow-xl z-10 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
          style="display: none"
        >
          <p
            class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary hover:text-white cursor-pointer"
            @click="auth.logout.run()"
          >
            ออกจากระบบ
          </p>
        </div>
      </div>
    </div>
  </header>
</template>
<script lang="ts" setup>
import { useRouter } from '#app'
import { useAuth } from '~/hooks/useAuth'
import { ref, StringHelper } from '#imports'
import { useWatchTrue } from '~/hooks/useWatch'

defineProps<{
  sidebarOpen: boolean
  setSidebar: (bool: boolean) => void
}>()

const emit = defineEmits<{
  (e: 'sidebarOpen'): void
}>()

const dropdownOpen = ref(false)
const auth = useAuth()
const router = useRouter()

useWatchTrue(
  () => auth.logout.status.isLoaded,
  () => {
    router.push('/login')
  }
)
</script>
