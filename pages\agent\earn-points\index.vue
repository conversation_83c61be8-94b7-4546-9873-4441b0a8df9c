<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentEarnPoints />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentEarnPoints from '~/features/agent/AgentEarnPoints/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentEarnPoints.name })
app.updatePageMeta({
  title: routes.agentEarnPoints.name,
  breadcrumbs: [routes.agentEarnPoints],
})
</script>
