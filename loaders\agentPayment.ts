import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { useRequestOptions } from '~/hooks/useRequestOptions'
import { type IPaymentItem } from '~/models/payment'

export const useAgentPaymentPageLoader = defineStore('agent.payments.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IPaymentItem>({
    baseURL: 'agent/payments',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})
