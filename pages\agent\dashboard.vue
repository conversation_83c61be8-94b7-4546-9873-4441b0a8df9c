<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentDashboard />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentDashboard from '~/features/agent/AgentDashboard/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentDashboard.name })
app.updatePageMeta({
  title: routes.agentDashboard.name,
  breadcrumbs: [routes.agentDashboard],
})
</script>
