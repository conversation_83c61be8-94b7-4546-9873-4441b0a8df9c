import { type IMasterBankItem } from '~/models/bank'

export interface IPlayerItem {
  id: string
  created_at: string
  updated_at: string
  agent_id: string
  account_api: string
  rank: string
  tel: string
  credit: number
  credit_hold: number
  credit_free: number
  last_active?: string
  last_ip: string
  title: string
  first_name: string
  last_name: string
  birthdate?: string
  bank_id: string
  bank_number: string
  recommend: string
  line: string
  is_promotion_enabled: boolean
  username: string
  register_ref: string
  agent: any
  bank: IMasterBankItem
}
