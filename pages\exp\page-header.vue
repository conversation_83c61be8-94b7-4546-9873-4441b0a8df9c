<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Page Header #1" />
    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h3 class="topic-heading my-2">Only Title</h3>
      <pre class="bg-gray-fill pt-8">
        &lt;PageHeader title="Your Page Title"/&gt;
      </pre>
    </div>

    <PageHeader class="mt-16" title="Page Header #2">
      <template #right>
        <Button class="btn-primary"> Some Button </Button>
      </template>
    </PageHeader>
    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h3 class="topic-heading my-2">Title | Action (right)</h3>
      <pre class="bg-gray-fill pt-8">
        &lt;PageHeader title="Your Page Title"&gt;
          &lt;template #right&gt;
            &lt;Button class="btn-primary"&gt;Some Button&lt;/Button&gt;
          &lt;/template&gt;
        &lt;/PageHeader&gt;
      </pre>
    </div>

    <PageHeader class="mt-16" title="Page Header #3">
      <span class="text-description">Some description</span>
      <template #right>
        <Button class="btn-primary"> Some Button </Button>
      </template>
    </PageHeader>
    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h3 class="topic-heading my-2">Title + เนื้อหาบางอย่างต่อจากชื่อเพจ | Action (right)</h3>
      <pre class="bg-gray-fill pt-8">
        &lt;PageHeader title="Your Page Title"&gt;
          &lt;span class="text-description"&gt;Some description&lt;/span&gt;
          &lt;template #right&gt;
            &lt;Button class="btn-primary"&gt;Some Button&lt;/Button&gt;
          &lt;/template&gt;
        &lt;/PageHeader&gt;
      </pre>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.modal.name })
app.updatePageMeta({
  title: styleguideMenu.modal.name,
  breadcrumbs: [styleguideMenu.modal],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))
</script>
