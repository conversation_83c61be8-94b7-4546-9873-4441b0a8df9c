<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      บันทึก
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IUserItem, IUserRole } from '~/models/user'
import { userRolesAgent } from '~/constants/roles'
import { useAdminAgentPositionPageLoader } from '~/loaders/agentRole'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item: IUserItem
}>()

const agentId = useRoute().params.id as string

const role = useAdminAgentPositionPageLoader(agentId)

role.setFetchLoading()
onMounted(() => {
  role.fetch()
})

const form = useForm<Partial<IUserItem>>({
  initialValues: props.item,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'full_name',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Username',
      name: 'username',
      isDisabled: true,
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ตำแหน่ง',
      name: 'role',
      rules: yup.string().required(),
      options: userRolesAgent,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    isHide: form.values.role === IUserRole.SUPER_AGENT,
    props: {
      label: 'role',
      name: 'user_position',
      rules: yup.string().required(),
      options: role.fetchItems.value.map((item) => ({
        label: item.name,
        value: item.id,
      })),
      isLoading: role.fetchStatus.value.isLoading,
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
