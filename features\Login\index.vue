<template>
  <div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <img
        class="mx-auto h-12 w-auto"
        src="https://tailwindui.com/img/logos/mark.svg?color=indigo&shade=600"
        alt="Your Company"
      />
      <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">เข้าสู่ระบบ</h2>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <form class="space-y-6 form" @submit="onSubmit">
          <FormFields :options="formFields" />
          <div>
            <Button
              type="submit"
              :is-loading="auth.login.status.isLoading"
              class="w-full btn-primary"
            >
              เข้าสู่ระบบ
            </Button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { useRouter } from '#app'
import { ILoginPayload, useAuth } from '~/hooks/useAuth'
import { INPUT_TYPES } from '~/components/Form/types'
import { useWatchTrue } from '~/hooks/useWatch'
import { useDialog } from '~/hooks/useDialog'
import { StringHelper } from '~/utils/StringHelper'
import Button from '~/components/Button.vue'
import { IUserRole } from '~/models/user'

const auth = useAuth()
const router = useRouter()
const dialog = useDialog()

const form = useForm<ILoginPayload>({
  initialValues: {
    username: 'admin',
    password: '12345678',
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Username',
      name: 'username',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'Password',
      name: 'password',
      rules: yup.string().required(),
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  auth.login.run(values)
})

useWatchTrue(
  () => auth.login.status.isSuccess,
  () => {
    if ([IUserRole.AGENT, IUserRole.SUPER_AGENT].includes(auth.me.data?.role as IUserRole)) {
      navigateTo(routes.agentDashboard.to, { external: true })
    }

    navigateTo(routes.overview.to, { external: true })
  }
)

useWatchTrue(
  () => auth.login.status.isError,
  () => {
    dialog.error({
      title: 'เข้าสู่ระบบไม่สำเร็จ',
      message: StringHelper.getError(auth.login.status.errorData),
    })
  }
)
</script>
