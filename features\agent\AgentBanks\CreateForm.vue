<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      สร้าง
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { useBankListLoader } from '~/loaders/bank'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const bank = useBankListLoader()

bank.setLoading()

onMounted(() => {
  bank.run()
})

const form = useForm<
  Partial<{
    bank_id: string
    account_number: string
    account_name: string
    allowed_bank: string[]
    type: string
    min_balance: number
    fee: number
    enabled_withdraw_otp: boolean
    enabled_show_on_web: boolean
  }>
>()

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ธนาคาร',
      name: 'bank_id',
      rules: yup.string().required(),
      options: ArrayHelper.toOptions(bank.items.value, 'id', 'name_th'),
      isLoading: bank.status.value.isLoading,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เลขบัญชี',
      name: 'account_number',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อบัญชี',
      name: 'account_name',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.MULTI_SELECT,
    props: {
      label: 'ธนาคารที่อนุญาติให้เห็น',
      name: 'allowed_bank',
      rules: yup.array().min(1).required(),
      options: ArrayHelper.toOptions(bank.items.value, 'slug', 'name_th'),
      isLoading: bank.status.value.isLoading,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ประเภท',
      name: 'type',
      options: [
        {
          label: 'บัญชีฝากและถอน',
          value: 'deposit_and_withdraw',
        },
        {
          label: 'บัญชีฝาก',
          value: 'deposit',
        },
        {
          label: 'บัญชีถอน',
          value: 'withdraw',
        },
        {
          label: 'บัญชีพักเงิน',
          value: 'idle',
        },
      ],
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'สำหรับลูกค้าที่มียอดฝาก',
      name: 'min_balance',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ค่าธรรมเนียม',
      name: 'fee',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    isHide: true,
    props: {
      label: 'เปิดใช้งาน OTP ถอน',
      name: 'enabled_withdraw_otp',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      label: 'เปิดใช้งาน (แสดงหน้าเว็บ)',
      name: 'enabled_show_on_web',
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    allowed_bank: ArrayHelper.toArray(data.allowed_bank).map((item) => item.value),
    min_balance: Number(data.min_balance),
    fee: Number(data.fee),
  })
})
</script>
