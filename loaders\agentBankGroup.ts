import { usePageLoader } from '~/lib/api/loaderPage'
import { type IBankGroupItem } from '~/models/bankGroup'

export const useAgentBankGroupPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IBankGroupItem>({
    baseURL: 'agent/group-banks',
    find: {
      getURL: (id) => `agent/group-banks/${id}`,
    },
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}
