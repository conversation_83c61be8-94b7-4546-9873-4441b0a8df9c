<template>
  <Form @submit="onSubmit">
    <FormFields :form="form" :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      {{ props.item ? 'บันทึก' : 'สร้าง' }}
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IUserItem, IUserRole } from '~/models/user'
import { userRolesAgent } from '~/constants/roles'
import { useAgentPositionPageLoader } from '~/loaders/agentRole'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item?: IUserItem
  agent: string
}>()

const role = useAgentPositionPageLoader()

role.setFetchLoading()
const form = useForm<
  Partial<{
    username: string
    password: string
    full_name: string
    position: string
    role: IUserRole
  }>
>({
  initialValues: {
    role: IUserRole.AGENT,
    ...props.item,
  },
})

onMounted(() => {
  role.fetch()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'full_name',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Username',
      name: 'username',
      isDisabled: !!props.item,
      prependIcon:
        !props.item &&
        defineComponent({
          setup() {
            return () => <p class="font-bold lowercase">{props.agent}</p>
          },
        }),
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    isHide: !!props.item,
    props: {
      label: 'password',
      name: 'password',
      rules: yup.string().min(8).required(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ตำแหน่ง',
      name: 'role',
      rules: yup.string().required(),
      options: userRolesAgent,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    isHide: form.values.role === IUserRole.SUPER_AGENT,
    props: {
      label: 'role',
      name: 'position',
      rules: yup.string().required(),
      options: role.fetchItems.value.map((item) => ({
        label: item.name,
        value: item.id,
      })),
      isLoading: role.fetchStatus.value.isLoading,
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', data)
})
</script>
