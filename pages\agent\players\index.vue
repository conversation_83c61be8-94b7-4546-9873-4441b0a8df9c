<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentPlayer />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentPlayer from '~/features/agent/AgentPlayers/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentPlayers.name })
app.updatePageMeta({
  title: routes.agentPlayers.name,
  breadcrumbs: [routes.agentPlayers],
})
</script>
