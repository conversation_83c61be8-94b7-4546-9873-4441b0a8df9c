import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IBankItem } from '~/models/bank'
import { useObjectLoader } from '~/lib/api/loaderObject'
import { useRequestOptions } from '~/hooks/useRequestOptions'

export const useAgentBankPageLoader = defineStore('agent.banks.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IBankItem>({
    baseURL: 'agent/banks',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentBankSCBRequestOTPLoader = (bankId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem>({
    url: `/agent/banks/${bankId}/scb/request-otp`,
    method: 'POST',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentBankSCBSubmitOTPLoader = (bankId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem, { otp: string }>({
    url: `/agent/banks/${bankId}/scb/submit-otp`,
    method: 'POST',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentBankSCBDeviceEnabledLoader = (bankId: string, deviceId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem, { otp: string }>({
    url: `/agent/banks/${bankId}/scb/${deviceId}/enabled`,
    method: 'POST',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
