<template>
  <div>
    <PageHeader :title="routes.agentPayments.name" />
    <div class="flex justify-end">
      <Modal
        v-model="isShowCreateModal"
        class="modal-md"
        title="เพิ่ม payment gateway"
        :no-backdrop-close="true"
      >
        <CreateForm :status="payment.addStatus" @submit="payment.add" />
      </Modal>
      <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true"> เพิ่ม </Button>
    </div>
    <Table :options="tableOptions" @pageChange="payment.fetch" @search="payment.search" />
  </div>
</template>
<script lang="tsx" setup>
import CreateForm from './CreateForm.vue'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'

import { useAgentPaymentPageLoader } from '~/loaders/agentPayment'
import { IPaymentItem } from '~/models/payment'

const payment = useAgentPaymentPageLoader()
const dialog = useDialog()
const router = useRouter()
const isShowCreateModal = ref(false)

payment.setFetchLoading()
onMounted(() => {
  payment.fetch()
})

useWatchTrue(
  () => payment.addStatus.isSuccess,
  () => {
    isShowCreateModal.value = false

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้าง Payment สำเร็จ',
      })
      .then(() => {
        payment.fetch()
      })
  }
)

useWatchTrue(
  () => payment.addStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(payment.addStatus.errorData),
    })
  }
)

const tableOptions = useTable<IPaymentItem>({
  repo: payment,
  columns: () => [
    {
      value: 'ชื่อ',
    },
    {
      value: 'ประเภท',
    },
    {
      value: 'เปิด/ปิด',
    },
    {
      value: 'ฝากออโต้',
    },
    {
      value: 'ถอนออโต้',
    },
    {
      value: 'ลำดับ',
      className: 'text-right',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.name,
        },
        {
          value: item.type,
        },
        {
          type: COLUMN_TYPES.BOOLEAN,
          value: item.is_enabled,
        },
        {
          type: COLUMN_TYPES.BOOLEAN,
          value: item.is_deposit_auto,
        },
        {
          type: COLUMN_TYPES.BOOLEAN,
          value: item.is_withdraw_auto,
        },
        {
          type: COLUMN_TYPES.NUMBER,
          value: item.sort_order,
          className: 'text-right',
        },
        {
          type: COLUMN_TYPES.ACTION,
          props: {
            isHideDelete: true,
          },
          on: {
            edit: () => {
              router.push(routes.agentPaymentEdit(item.id).to)
            },
          },
        },
      ]
    }),
})
</script>
