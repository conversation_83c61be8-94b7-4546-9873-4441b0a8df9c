<template>
  <div>
    <PageHeader :title="routes.agentPromotions.name" />
    <Table :options="tableOptions" @pageChange="promotion.fetch" @search="promotion.search">
      <template #toolbar>
        <div class="flex justify-end items-center space-x-2">
          <Button class="btn-primary btn-min" @click="isShowSortModal = true">จัดเรียง</Button>
          <Button class="btn-primary btn-min" @click="handleOpenCreate">สร้าง</Button>
        </div>
      </template>
    </Table>

    <Modal
      v-model="isShowCreateModal"
      class="modal-lg"
      body-class="p-0"
      :title="currentItem ? 'แก้ไขโปรโมชั่น' : 'สร้างโปรโมชั่น'"
      :no-backdrop-close="true"
    >
      <CreateForm :item="currentItem" @success="handleCreateSuccess" />
    </Modal>

    <Modal
      v-model="isShowSortModal"
      class="modal-lg"
      body-class="h-[90vh]"
      title="จัดเรียงโปรโมชั่น"
      :no-backdrop-close="true"
    >
      <div class="flex flex-col justify-between h-full">
        <div class="overflow-auto">
          <SortOrder :items="currentSortedList" @sorted="handleOnSorted" />
        </div>
        <div class="mt-4 w-full">
          <Button
            class="btn-primary w-full"
            :is-loading="promotionSort.status.value.isLoading"
            :is-disabled="currentSortedList.length === 0"
            @click="handleOnSave"
          >
            บันทึก
          </Button>
        </div>
      </div>
    </Modal>

    <Modal v-model="isShowImageModal" class="modal-lg" body-class="p-0">
      <div v-if="currentImageURL">
        <img class="w-full object-contain" :src="currentImageURL" alt="show-image" />
      </div>
      <div v-else class="flex items-center justify-center h-32">
        <p class="text-2xl">ไม่มีรูปภาพ</p>
      </div>
    </Modal>
  </div>
</template>

<script lang="tsx" setup>
import ColumnAction from './ColumnAction.vue'
import CreateForm from './CreateForm.vue'
import SortOrder from './SortOrderList.vue'
import Button from '~/components/Button.vue'
import { IPromotionItem } from '~/models/promotion'
import { COLUMN_TYPES } from '~/components/Table/types'
import Badge from '~/components/Badge.vue'
import { routes } from '~/constants/routes'

const promotion = useAgentPromotionPageLoader()
const promotionSort = useAgentPromotionSortLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)
const isShowSortModal = ref(false)
const isShowImageModal = ref(false)

const currentImageURL = ref<string>('')
const currentSortedList = ref<string[]>([])
const currentItem = ref<IPromotionItem | undefined>(undefined)

promotion.setFetchLoading()
onMounted(() => {
  promotion.fetch(1, undefined)
})

const handleCreateSuccess = () => {
  isShowCreateModal.value = false
  promotion.fetch(1, undefined)
}

const tableOptions = useTable<IPromotionItem>({
  repo: promotion,
  columns: () => [
    {
      value: 'ลำดับ',
      className: 'text-center',
    },
    {
      value: 'ชื่อโปรโมชั่น',
    },
    {
      value: 'ประเภทโปรโมชั่น',
    },
    {
      value: 'ประเภทผู้เล่น',
      className: 'text-center',
    },
    {
      value: 'สถานะ',
      className: 'text-center',
    },
    {
      value: 'วันที่เริ่ม',
      className: 'text-center',
    },
    {
      value: 'วันที่สิ้นสุด',
      className: 'text-center',
    },
    {
      value: 'สถานะ',
      className: 'text-center',
    },
    {
      value: 'แสดง',
      className: 'text-center',
    },
    {
      value: '',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item: IPromotionItem) => {
      return [
        {
          value: item.sort_order + 1,
          className: 'text-center w-10',
        },
        {
          value: item.name_th || item.name_en,
        },
        {
          value: PROMOTION_TYPE_DISPLAY[item.promotion_type],
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          value: defineComponent(() => () => (
            <div class={'flex justify-center'}>
              <Badge class={'whitespace-nowrap bg-black capitalize'}>
                {USER_TYPE_RECEIVER_DISPLAY[item.user_type_receiver]}
              </Badge>
            </div>
          )),
          className: 'text-center',
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          value: defineComponent(() => () => (
            <div class={'flex justify-center'}>
              <Badge class={`whitespace-nowrap ${item.is_active ? 'bg-success' : 'bg-danger'}`}>
                {item.is_active ? 'เปิดใช้งาน' : 'ปิดใช้งาน'}
              </Badge>
            </div>
          )),
          className: 'text-center',
        },
        {
          type: COLUMN_TYPES.DATE_TIME,
          value: item.start_date,
          className: 'text-center w-10',
        },
        {
          type: COLUMN_TYPES.DATE_TIME,
          value: item.end_date,
          className: 'text-center w-10',
        },
        {
          type: COLUMN_TYPES.BOOLEAN,
          value: item.is_active,
          className: 'text-center w-10',
        },
        {
          type: COLUMN_TYPES.BOOLEAN,
          value: item.is_display,
          className: 'text-center w-10',
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          className: 'text-center',
          value: defineComponent(() => () => (
            <div onClick={() => handlerOpenImage(item.image_url)}>
              <Button icon="ic ic-eye-solid" isOnlyIcon class={'bg-blue-400 text-white'} />
            </div>
          )),
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          value: ColumnAction,
          on: {
            edit: () => {
              promotion.find(item.id)
            },
            delete: () => {
              dialog
                .warning({
                  title: 'ยืนยันการลบโปรโมชั่น',
                  isShowCancelBtn: true,
                })
                .then(() => {
                  promotion.remove(item.id)
                })
            },
          },
        },
      ]
    }),
  options: {
    isHideToolbar: false,
  },
})

const handleOpenCreate = () => {
  currentItem.value = undefined
  isShowCreateModal.value = true
}

const handlerOpenImage = (url: string) => {
  currentImageURL.value = url
  isShowImageModal.value = true
}

useWatchTrue(
  () => promotion.findStatus.isSuccess,
  () => {
    currentItem.value = promotion.findItem as IPromotionItem
    isShowCreateModal.value = true
  }
)

useWatchTrue(
  () => promotion.findStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(promotion.findStatus.errorData),
    })
  }
)

useWatchTrue(
  () => promotion.deleteStatus.isSuccess,
  () => {
    promotion.fetch(1, undefined)
  }
)

useWatchTrue(
  () => promotion.deleteStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(promotion.deleteStatus.errorData),
    })
  }
)

const handleOnSorted = (ids: string[]) => {
  currentSortedList.value = ids
}

const handleOnSave = () => {
  promotionSort.run({ promotion_ids: currentSortedList.value })
}

useWatchTrue(
  () => promotionSort.status.value.isSuccess,
  () => {
    currentSortedList.value = []
    promotion.fetch(1, undefined)
    isShowSortModal.value = false
  }
)

useWatchTrue(
  () => promotionSort.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(promotionSort.status.value.errorData),
    })
  }
)
</script>
