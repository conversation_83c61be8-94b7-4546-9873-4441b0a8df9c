<template>
  <thead :class="['table-header', className]">
    <tr>
      <th
        v-if="!status.isLoading && status.isSuccess && isShowCheckbox"
        class="checkbox"
        scope="col"
      >
        <div class="block w-12">
          <input
            v-model="isCheckAll"
            class="checkbox-control"
            type="checkbox"
            :disabled="rowsIndex?.length === 0"
          />
        </div>
      </th>
      <th
        v-for="(column, index) in columns"
        :key="index"
        :class="['table-header-column', column.className]"
        scope="col"
      >
        <component :is="column.value" v-if="column.isComponent" v-bind="column.props" />
        <p v-else v-html="column.value" />
      </th>
    </tr>
  </thead>
</template>

<script lang="ts" setup>
import { PropType } from 'vue'
import { IColumn, IStatus } from './types'
import { _xor } from '~/utils/lodash'
import { computed } from '#imports'

const props = defineProps({
  className: { type: [String, Object] },
  columns: { type: Array as PropType<IColumn[]> },
  rowsIndex: { type: Array as PropType<number[]>, default: () => [] },
  rowsIndexSelect: { type: Array as PropType<number[]>, default: () => [] },
  status: { type: Object as PropType<IStatus>, required: true },
  isShowCheckbox: { type: Boolean, default: false },
})

const selectedIndex = ref(props.rowsIndexSelect)

const isCheckAll = computed<boolean>({
  get() {
    if (props.rowsIndex.length === 0) {
      return false
    }

    return props.rowsIndex.length === props.rowsIndexSelect.length
  },
  set(value) {
    const insertIndex = _xor(props.rowsIndex, props.rowsIndexSelect)

    value
      ? selectedIndex.value.push(...insertIndex)
      : selectedIndex.value.splice(0, props.rowsIndexSelect.length)
  },
})
</script>
