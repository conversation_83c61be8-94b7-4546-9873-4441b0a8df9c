import { type IFieldProps, type IFormFieldBase, type INPUT_TYPES } from '~/components/Form/types'

export interface IUploadDropzoneAutoProps extends IFieldProps {
  dropzoneClassName?: ClassName
  accept?: string[] | string
  // in kb
  maxSize?: number
}

export type IUploadDropzoneAuto = IFormFieldBase<
  INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
  IUploadDropzoneAutoProps,
  {
    change: (url: string | null) => void
  }
>
