<template>
  <Form @submit="onSubmit">
    <FormFields class="grid grid-cols-2 gap-4" :options="formFields" />
    <Button type="submit" class="btn-primary btn-min" :is-loading="status.isLoading">
      บันทึก
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IAgentItem } from '~/models/agent'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item: IAgentItem
}>()

const form = useForm<
  Partial<{
    name: string
    prefix: string
    domain: string
    credit: number
    remark?: string
  }>
>({
  initialValues: props.item,
})

watch(
  () => props.item,
  (item) => {
    form.setValues(item)
  }
)

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'name',
      isDisabled: true,
      rules: yup.string().min(3).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Prefix',
      name: 'prefix',
      isDisabled: true,
      rules: yup.string().noSpaces().english().uppercase().min(2).max(5).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'โดเมน',
      name: 'domain',
      isDisabled: true,
      rules: yup.string().url().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เครดิต',
      name: 'credit',
      isDisabled: true,
      rules: yup.string().numeric().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      label: 'หมายเหตุ',
      name: 'remark',
      rules: yup.string(),
      rows: 5,
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    ...data,
    prefix: data.prefix?.toUpperCase(),
    credit: Number(data.credit),
  })
})
</script>
