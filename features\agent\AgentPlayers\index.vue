<template>
  <div>
    <PageHeader :title="routes.agentPlayers.name" />
    <div class="flex justify-end">
      <Modal
        v-model="isShowCreateModal"
        class="modal-md"
        title="สร้างผู้เล่น"
        :no-backdrop-close="true"
      >
        <CreateForm :status="player.addStatus" @submit="player.add" />
      </Modal>
      <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">สร้าง</Button>
    </div>
    <Table :options="tableOptions" @pageChange="player.fetch" @search="player.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import CreateForm from '~/features/agent/AgentPlayers/CreateForm.vue'
import { useAgentPlayerPageLoader } from '~/loaders/player'
import { IPlayerItem } from '~/models/player'
import ColumnAction from '~/features/agent/AgentPlayers/ColumnAction.vue'

const player = useAgentPlayerPageLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)

player.setFetchLoading()
onMounted(() => {
  player.fetch()
})

useWatchTrue(
  () => player.addStatus.isSuccess,
  () => {
    isShowCreateModal.value = false

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างผู้เล่นสำเร็จ',
      })
      .then(() => {
        player.fetch()
      })
  }
)

useWatchTrue(
  () => player.addStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(player.updateStatus.errorData),
    })
  }
)

const tableOptions = useTable<IPlayerItem>({
  repo: player,
  columns: () => [
    {
      value: 'username',
    },
    {
      value: 'เบอร์โทร',
    },
    {
      value: 'ธนาคาร',
    },
    {
      value: 'Rank',
    },
    {
      value: 'เครดิต',
      className: 'text-right',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: 'การจัดการ',
      className: 'text-center',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item,
          type: COLUMN_TYPES.PLAYER,
        },
        {
          value: item.tel,
        },
        {
          value: {
            image_url: item.bank.image_url,
            name: item.first_name + ' ' + item.last_name,
            number: item.bank_number,
            bank: item.bank.name_th,
          },
          type: COLUMN_TYPES.BANK,
        },
        {
          value: item.rank,
        },
        {
          value: item.credit,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          className: 'text-center',
          props: {
            item,
          },
        },
      ]
    }),
})
</script>
