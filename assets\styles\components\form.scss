@layer components {
  .form {
    .form-container {
      @apply relative mb-4;
    }

    .form-control {
      @apply appearance-none ring-1 ring-gray-500 px-3 ring-inset py-3 text-gray-600 bg-white rounded-md text-sm placeholder-gray-400 focus:outline-none w-full;
    }

    .form-label {
      @apply block text-gray-500 font-medium text-sm mb-2 whitespace-nowrap;
    }

    .input-error {
      @apply ring-danger;
    }

    .input-success {
      @apply ring-success;
    }

    .invalid-feedback {
      @apply text-danger text-sm mt-2;
    }
  }
}
