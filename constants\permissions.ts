export const agentPermissions = {
  userRead: 'agent:user.read',
  userCreate: 'agent:user.create',
  userUpdate: 'agent:user.update',
  userChangePassword: 'agent:user.change-password',
  userEnabled: 'agent:user.enabled',
  userDelete: 'agent:user.delete',
  playerRead: 'agent:player.read',
  playerCreate: 'agent:player.create',
  playerUpdate: 'agent:player.update',
  playerDelete: 'agent:player.delete',
  playerUpdateCredit: 'agent:player.update-credit',
  playerGetBalance: 'agent:player.get-balance',
  playerEnabled: 'agent:player.enabled',
  providerRead: 'agent:provider.read',
  providerCreate: 'agent:provider.create',
  providerUpdate: 'agent:provider.update',
  providerDelete: 'agent:provider.delete',
  providerEnabled: 'agent:provider.enabled',
  roleRead: 'agent:role.read',
  roleCreate: 'agent:role.create',
  groupBankRead: 'agent:group-bank.read',
  groupBankToggle: 'agent:group-bank.toggle',
  bankRead: 'agent:bank.read',
  bankCreate: 'agent:bank.create',
  bankUpdate: 'agent:bank.update',
  bankDelete: 'agent:bank.delete',
  bankToggleShowOnWeb: 'agent:bank.toggle-show-on-web',
  bankToggleWithdrawAuto: 'agent:bank.toggle-withdraw-auto',
  bankToggleMobileApplication: 'agent:bank.toggle-mobile-application',
  bankToggleLimit: 'agent:bank.toggle-limit',
  userPositionRead: 'agent:user-position.read',
  userPositionCreate: 'agent:user-position.create',
  userPositionUpdate: 'agent:user-position.update',
  userPositionDelete: 'agent:user-position.delete',
  webConfigUpdate: 'agent:webconfig.update',
  noticeUpdate: 'agent:notice.update',

  // New permissions
  affiliateSettingsRead: 'agent:affiliate_settings.read',
  affiliateSettingsReadByType: 'agent:affiliate_settings.read_by_type',
  affiliateSettingsUpdate: 'agent:affiliate_settings.update',
  billingsCreate: 'agent:billings.create',
  billingsRead: 'agent:billings.read',
  billingsReadById: 'agent:billings.read_by_id',
  billingsUpdate: 'agent:billings.update',
  cashbackSettingsRead: 'agent:cashback_settings.read',
  cashbackSettingsReadByType: 'agent:cashback_settings.read_by_type',
  cashbackSettingsUpdate: 'agent:cashback_settings.update',
  agentCreditReportRead: 'admin:agent_credit_report.read',
  dashboardStatsOverall: 'agent:dashboard.stats_overall',
  dashboardStatsGraph: 'agent:dashboard.stats_graph',
  dashboardStatsBet: 'agent:dashboard.stats_bet',
  domainsRead: 'agent:domains.read',
  domainsCreate: 'agent:domains.create',
  domainsReadById: 'agent:domains.read_by_id',
  domainsUpdate: 'agent:domains.update',
  domainsDelete: 'agent:domains.delete',
  gamesCreateSetting: 'agent:games.create_setting',
  gamesReadSetting: 'agent:games.read_setting',
  playersReadDeposits: 'agent:players.read_deposits',
  playersReadWithdraws: 'agent:players.read_withdraws',
  playersReadBets: 'agent:players.read_bets',
  promotionsCreate: 'agent:promotions.create',
  promotionsRead: 'agent:promotions.read',
  promotionsReadAll: 'agent:promotions.read_all',
  promotionsReadById: 'agent:promotions.read_by_id',
  promotionsUpdate: 'agent:promotions.update',
  promotionsToggleEnabled: 'agent:promotions.toggle_enabled',
  promotionsToggleDisplay: 'agent:promotions.toggle_display',
  promotionsSort: 'agent:promotions.sort',
  promotionsDelete: 'agent:promotions.delete',
  specialPromotionsCreate: 'agent:special_promotions.create',
  specialPromotionsRead: 'agent:special_promotions.read',
  specialPromotionsReadAll: 'agent:special_promotions.read_all',
  specialPromotionsReadById: 'agent:special_promotions.read_by_id',
  specialPromotionsUpdate: 'agent:special_promotions.update',
  specialPromotionsToggleEnabled: 'agent:special_promotions.toggle_enabled',
  specialPromotionsToggleDisplay: 'agent:special_promotions.toggle_display',
  specialPromotionsSort: 'agent:special_promotions.sort',
  specialPromotionsDelete: 'agent:special_promotions.delete',
}
