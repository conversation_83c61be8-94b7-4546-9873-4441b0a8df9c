import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IAgentItem, type IAgentNoticeItem } from '~/models/agent'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useAgentPageLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IAgentItem>({
    baseURL: '/admin/agents',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentCreditPlus = (
  agentId: string,
  options: {
    isShowDialog?: boolean
    onBeforeSuccess?: () => void
  } = {}
) => {
  const { getDefaultWithAuth } = useRequestOptions()
  const dialog = useDialog()
  const agent = useAgentPageStore()
  const loader = useObjectLoader<IAgentItem, { credit: number }>({
    method: 'put',
    url: `/admin/agents/${agentId}/plus-credits`,
    getRequestOptions: () => getDefaultWithAuth(),
  })

  if (options.isShowDialog) {
    useWatchTrue(
      () => loader.status.value.isSuccess,
      () => {
        options?.onBeforeSuccess?.()
        void dialog
          .success({
            title: 'สำเร็จ',
            message: 'เพิ่มเครดิตสำเร็จ',
          })
          .then(() => {
            void agent.find(agentId)
          })
      }
    )

    useWatchTrue(
      () => loader.status.value.isError,
      () => {
        void dialog.error({
          title: 'เกิดข้อผิดพลาด',
          message: StringHelper.getError(loader.status.value.errorData),
        })
      }
    )
  }

  return loader
}

export const useAgentCreditMinus = (
  agentId: string,
  options: {
    isShowDialog?: boolean
    onBeforeSuccess?: () => void
  } = {}
) => {
  const { getDefaultWithAuth } = useRequestOptions()
  const dialog = useDialog()
  const agent = useAgentPageStore()

  const loader = useObjectLoader<IAgentItem, { credit: number }>({
    method: 'put',
    url: `/admin/agents/${agentId}/minus-credits`,
    getRequestOptions: () => getDefaultWithAuth(),
  })

  if (options.isShowDialog) {
    useWatchTrue(
      () => loader.status.value.isSuccess,
      () => {
        options?.onBeforeSuccess?.()
        void dialog
          .success({
            title: 'สำเร็จ',
            message: 'ลบเครดิตสำเร็จ',
          })
          .then(() => {
            void agent.find(agentId)
          })
      }
    )

    useWatchTrue(
      () => loader.status.value.isError,
      () => {
        void dialog.error({
          title: 'เกิดข้อผิดพลาด',
          message: StringHelper.getError(loader.status.value.errorData),
        })
      }
    )
  }

  return loader
}

export const useAgentEnabledUpdate = (
  agentId: string,
  options: {
    isShowDialog?: boolean
  } = {}
) => {
  const { getDefaultWithAuth } = useRequestOptions()
  const dialog = useDialog()
  const agent = useAgentPageStore()

  const loader = useObjectLoader<IAgentItem, { is_enabled: boolean }>({
    method: 'post',
    url: `/admin/agents/${agentId}/enabled`,
    getRequestOptions: () => getDefaultWithAuth(),
  })

  if (options.isShowDialog) {
    useWatchTrue(
      () => loader.status.value.isSuccess,
      () => {
        void dialog
          .success({
            title: 'สำเร็จ',
            message: 'อัพเดทสถานะเรียบร้อยแล้ว',
          })
          .then(() => {
            void agent.find(agentId)
          })
      }
    )

    useWatchTrue(
      () => loader.status.value.isError,
      () => {
        void dialog.error({
          title: 'ผิดพลาด',
          message: 'อัพเดทสถานะล้มเหลว',
        })
      }
    )
  }

  return loader
}

export const useAgentPageStore = defineStore('useAgentPage', useAgentPageLoader)

export const useAgentLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentItem>({
    method: 'get',
    url: '/agent',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentConfigUpdateLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentItem>({
    method: 'POST',
    url: '/agent/webconfig',
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentNoticeLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IAgentNoticeItem>({
    baseURL: 'agent/notice',
    getBaseRequestOptions: () => {
      return getDefaultWithAuth()
    },
  })
}
