<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentProviders />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentProviders from '~/features/agent/AgentProviders/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentProviders.name })
app.updatePageMeta({
  title: routes.agentProviders.name,
  breadcrumbs: [routes.agentProviders],
})
</script>
