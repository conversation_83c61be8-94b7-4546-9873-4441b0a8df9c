<template>
  <Table :options="tableOptions" @pageChange="onFetch" @search="onSearch">
    <template #toolbar>
      <div>
        <FormFields :form="form" :options="formFields" class="w-[250px]" />
      </div>
    </template>
  </Table>
</template>
<script lang="tsx" setup>
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import ColumnAction from './ColumnAction.vue'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { IAgentDepositItem } from '~/models/deposit'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'

const props = defineProps<{
  withdraw: useAgentWithdrawPageLoader
}>()

const form = useForm<
  Partial<{
    status: string
  }>
>({
  initialValues: {
    status: '',
  },
})

props.withdraw.setFetchLoading()
onMounted(() => {
  props.withdraw.fetch()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'status',
      placeholder: 'สถานะ',
      label: '',
      rules: yup.string(),
      options: [
        {
          label: 'ทั้งหมด',
          value: '',
        },
        {
          label: 'pending',
          value: 'pending',
        },
        {
          label: 'rejected',
          value: 'rejected',
        },
        {
          label: 'approved',
          value: 'approved',
        },
      ],
    },
  },
])

const tableOptions = useTable<IAgentDepositItem>({
  options: {
    isNotChangeRoute: true,
  },
  repo: props.withdraw,
  columns: () => [
    {
      value: '#',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'ผู้ถอน',
    },
    {
      value: 'บัญชี',
    },
    {
      value: 'จำนวนเครดิต',
      className: 'text-right',
    },
    {
      value: 'คงเหลือ (ก่อน-หลัง)',
      className: 'text-right',
    },
    {
      value: 'หมายเหตุ',
    },
    {
      value: 'เวลาแจ้ง',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.id,
          props: {
            max: 11,
          },
          className: 'text-xs',
        },
        {
          value: item.status,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.player ? item.player : '-',
          type: item.player ? COLUMN_TYPES.PLAYER : COLUMN_TYPES.VALUE,
        },
        {
          value: item.player?.bank
            ? {
                image_url: item.player.bank?.image_url,
                name: item.player.first_name + ' ' + item.player.last_name,
                number: item.player.bank_number,
                bank: item.player.bank?.name_th,
              }
            : '-',
          type: item.player?.bank ? COLUMN_TYPES.BANK : COLUMN_TYPES.VALUE,
        },
        {
          value: item.amount,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value:
            item.status === 'approved'
              ? `${StringHelper.withComma(item.before_amount)} - ${StringHelper.withComma(
                  item.after_amount
                )}`
              : '-',
          className: 'text-right',
        },
        {
          value: item.remark,
          className: 'text-right',
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            item,
            onDone: () => {
              props.withdraw.fetch()
            },
          },
        },
      ]
    }),
})

const onFetch = (page = 1, search = props.withdraw.fetchOptions.search) => {
  props.withdraw.fetch(page, search, {
    params: {
      status: form.values.status,
    },
  })
}

const onSearch = (search: string) => {
  onFetch(1, search)
}

watch(
  () => form.values.status,
  () => {
    onFetch()
  }
)
</script>
