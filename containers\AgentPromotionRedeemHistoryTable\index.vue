<template>
  <Table :options="tableOptions" @pageChange="onFetch" @search="onSearch">
    <template #toolbar>
      <div>
        <FormFields :form="form" :options="formFields" class="w-[250px]" />
      </div>
    </template>
  </Table>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { useAgentPromotionRedeemHistoryPageLoader } from '~/loaders/agentPromotion'
import { IPromotionRedeemItem } from '~/models/promotion'

const props = defineProps<{
  loader: useAgentPromotionRedeemHistoryPageLoader
}>()

const form = useForm<
  Partial<{
    status: string
  }>
>({
  initialValues: {
    status: '',
  },
})

props.loader.setFetchLoading()
onMounted(() => {
  props.loader.fetch()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'status',
      placeholder: 'สถานะ',
      label: '',
      rules: yup.string(),
      options: [
        {
          label: 'ทั้งหมด',
          value: '',
        },
        {
          label: 'on going',
          value: 'on_going',
        },
        {
          label: 'done',
          value: 'done',
        },
        {
          label: 'cancel',
          value: 'cancel',
        },
      ],
    },
  },
])

const tableOptions = useTable<IPromotionRedeemItem>({
  options: {
    isNotChangeRoute: true,
  },
  repo: props.loader,
  columns: () => [
    {
      value: 'ชื่อ',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'วันที่ได้รับ',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.promotion?.name || '-',
        },
        {
          value: item.status,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
      ]
    }),
})

const onFetch = (page = 1, search = props.loader.fetchOptions.search) => {
  props.loader.fetch(page, search, {
    params: {
      status: form.values.status,
    },
  })
}

const onSearch = (search: string) => {
  onFetch(1, search)
}

watch(
  () => form.values.status,
  () => {
    onFetch()
  }
)
</script>
