<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <Home />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import Home from '../features/Home/index.vue'
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import { IUserRole } from '~/models/user'

definePageMeta({
  middleware: MIDDLEWARES.AUTH,
})

const app = useApp()
const auth = useAuth()

app.updateDocMeta({ title: routes.overview.name })
app.updatePageMeta({
  title: routes.overview.name,
  breadcrumbs: [routes.overview],
})

onMounted(() => {
  if ([IUserRole.AGENT, IUserRole.SUPER_AGENT].includes(auth.me.data?.role as IUserRole)) {
    navigateTo(routes.agentDashboard.to, { external: true })
  }
})
</script>
