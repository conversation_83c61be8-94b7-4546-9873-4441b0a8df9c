<template>
  <FieldWrapper v-bind="wrapperProps">
    <Vue3ColorPicker
      v-model="value"
      mode="solid"
      :show-color-list="false"
      :show-eye-drop="false"
      :show-alpha="false"
      type="HEX"
      :disabled="disabled"
    />
  </FieldWrapper>
</template>
<script lang="ts" setup>
import { Vue3ColorPicker } from '@cyhnkckali/vue3-color-picker'
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { ITextFieldProps } from '~/components/Form/text_field.types'
import '@cyhnkckali/vue3-color-picker/dist/style.css'

const props = withDefaults(defineProps<ITextFieldProps>(), {})
const emits = defineEmits<{
  (event: 'change', value: string): void
}>()

const { value, wrapperProps, onChange, fieldClassName, disabled } = useFieldHOC<string>(props)
</script>
