import { type IBankItem } from '~/models/bank'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useAgentBankToggleShowOnWebObjectLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem>({
    method: 'PUT',
    url: 'agent/banks/:id/toggle-show-on-web',
    getURL: (data) => `agent/banks/${data.id}/toggle-show-on-web`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentBankToggleWithdrawAutoObjectLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem>({
    method: 'PUT',
    url: 'agent/banks/:id/toggle-withdraw-auto',
    getURL: (data) => `agent/banks/${data.id}/toggle-withdraw-auto`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentBankToggleToggleMobileApplicationObjectLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem>({
    method: 'PUT',
    url: 'agent/banks/:id/toggle-mobile-application',
    getURL: (data) => `agent/banks/${data.id}/toggle-mobile-application`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentBankToggleLimitObjectLoader = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem>({
    method: 'PUT',
    url: 'agent/banks/:id/toggle-limit',
    getURL: (data) => `agent/banks/${data.id}/toggle-limit`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
