<template>
  <div>
    <PageHeader :title="routes.agentCreditReports.name" />
    <Table :options="tableOptions" @pageChange="credit.fetch" @search="credit.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { useAgentCreditReportPageLoader } from '~/loaders/agentCreditReport'
import { IAgentCreditReportItem } from '~/models/agentCreditReport'

const credit = useAgentCreditReportPageLoader()

credit.setFetchLoading()
onMounted(() => {
  credit.fetch()
})

const tableOptions = useTable<IAgentCreditReportItem>({
  repo: credit,
  columns: () => [
    {
      value: 'Agent',
    },
    {
      value: 'การกระทำ',
    },
    {
      value: 'จำนวน',
    },
    {
      value: 'ก่อน',
    },
    {
      value: 'หลัง',
    },
    {
      value: 'จาก',
    },
    {
      value: 'สร้างเมื่อ',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.agent.name,
        },
        {
          value: item.action,
          className: item.action === 'minus' ? 'text-danger' : 'text-success',
        },
        {
          value: item.amount,
          type: COLUMN_TYPES.NUMBER,
          className: item.action === 'minus' ? 'text-danger' : 'text-success',
        },
        {
          value: item.before,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.after,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.user?.username,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
      ]
    }),
})
</script>
