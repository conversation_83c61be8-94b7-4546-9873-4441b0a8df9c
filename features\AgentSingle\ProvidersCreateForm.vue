<template>
  <Form @submit="onSubmit">
    <FormFields :form="form" :options="formFields" />
    <Button type="submit" class="btn-primary w-full btn-min" :is-loading="status.isLoading">
      เพิ่ม
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { useProviderPageLoader } from '~/loaders/provider'
import { IProviderItem } from '~/models/provider'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

defineProps<{
  status: IStatus
}>()

const provider = useProviderPageLoader()

provider.setFetchLoading()

onMounted(() => {
  provider.fetch(1, '', {
    params: {
      limit: 1000,
    },
  })
})

const form = useForm<
  Partial<{
    provider_id: string
    percent: number
  }>
>()

const selectedProvider = computed<IProviderItem | undefined>(() => {
  return provider.fetchItems.value.find((item) => item.id === form.values.provider_id)
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT_AUTO_COMPLETE,
    props: {
      label: 'ค่ายเกม',
      name: 'provider_id',
      rules: yup.string().required(),
      options: ArrayHelper.toOptions(provider.fetchItems.value, 'id', 'name'),
      isLoading: provider.fetchStatus.value.isLoading,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เปอเซนต์',
      name: 'percent',
      placeholder: form.values.provider_id
        ? `เปอเซนต์สูงสุด ${selectedProvider.value?.percent}%`
        : 'เลือกค่ายเกมก่อน',
      isDisabled: !form.values.provider_id,
      rules: yup.lazy((value) => {
        if (form.values.provider_id) {
          return yup
            .number()
            .integer()
            .min(1)
            .max(selectedProvider.value?.percent || 100)
            .required()
        }

        return yup.number().required()
      }),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    items: [
      {
        ...data,
        percent: Number(data.percent),
      },
    ],
  })
})
</script>
