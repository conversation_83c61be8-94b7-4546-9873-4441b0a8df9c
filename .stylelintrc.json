{"extends": ["stylelint-config-standard-scss", "stylelint-config-prettier-scss"], "rules": {"at-rule-no-unknown": null, "scss/at-rule-no-unknown": null, "declaration-block-trailing-semicolon": null, "no-descending-specificity": null, "selector-type-case": "lower", "no-invalid-position-at-import-rule": null, "selector-id-pattern": null, "selector-class-pattern": null, "property-no-vendor-prefix": null}, "ignoreFiles": ["coverage/**/*", ".nuxt/**/*", ".output/**/*"]}