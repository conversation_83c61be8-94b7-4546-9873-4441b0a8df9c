import { usePageLoader } from '~/lib/api/loaderPage'
import { type IAgentProviderItem } from '~/models/provider'
import { useObjectLoader } from '~/lib/api/loaderObject'

export const useAgentProviderPageLoader = (agentId?: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IAgentProviderItem>({
    baseURL: agentId ? `/admin/agents/${agentId}/providers` : 'agent/providers',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
}

export const useAgentProviderEnabledLoader = (agentId: string, agentProviderId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IAgentProviderItem, { is_enabled: boolean }>({
    method: 'post',
    url: `/admin/agents/${agentId}/providers/${agentProviderId}/enabled`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
