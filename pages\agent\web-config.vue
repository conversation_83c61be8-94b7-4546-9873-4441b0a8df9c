<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <AgentWebConfig />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import AgentWebConfig from '~/features/agent/AgentWebConfig/index.vue'

definePageMeta({
  middleware: MIDDLEWARES.AUTH_AGENT,
})

const app = useApp()

app.updateDocMeta({ title: routes.agentWebConfig.name })
app.updatePageMeta({
  title: routes.agentWebConfig.name,
  breadcrumbs: [routes.agentWebConfig],
})
</script>
