<template>
  <div>
    <div>
      <PageHeader :title="routes.agentEarnPoints.name" />
      <div class="flex justify-end">
        <Modal v-model="isShowCreateModal" class="modal-lg" title="สร้าง" :no-backdrop-close="true">
          <CreateForm :status="earnPoint.addStatus.value" @submit="earnPoint.add" />
        </Modal>
        <Modal v-model="isShowEditModal" class="modal-md" title="แก้ไข" :no-backdrop-close="true">
          <CreateForm
            :status="earnPoint.updateStatus.value"
            :item="isActiveItem"
            @submit="onUpdateSubmit"
          />
        </Modal>
        <Button class="btn-primary mb-4 btn-min" @click="isShowCreateModal = true">สร้าง</Button>
      </div>
      <Table :options="tableOptions" @pageChange="earnPoint.fetch" @search="earnPoint.search" />
    </div>
  </div>
</template>
<script lang="tsx" setup>
import CreateForm from './CreateForm.vue'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { useAgentEarnPointPageLoader } from '~/loaders/agentEarnpoint'
import { IEarnPointItem } from '~/models/earnPoint'

const earnPoint = useAgentEarnPointPageLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)
const isShowEditModal = ref(false)
const isActiveItem = ref<IEarnPointItem | undefined>(undefined)

earnPoint.setFetchLoading()
onMounted(() => {
  earnPoint.fetch()
})

const onUpdateSubmit = (data: IEarnPointItem) => {
  earnPoint.update(data.id, data)
}

const tableOptions = useTable<IEarnPointItem>({
  repo: earnPoint,
  columns: () => [
    {
      value: 'รูป',
    },
    {
      value: 'หัวข้อ',
    },
    {
      value: 'point',
    },
    {
      value: 'credit fee',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.title,
        },
        {
          value: item.point,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.credit,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          type: COLUMN_TYPES.ACTION,
          value: '',
          props: {
            isShowEdit: true,
          },
          on: {
            edit: () => {
              isActiveItem.value = item
              isShowEditModal.value = true
            },
            delete: () => {
              earnPoint.remove(item.id)
            },
          },
        },
      ]
    }),
})

useWatchTrue(
  () => earnPoint.addStatus.value.isSuccess,
  () => {
    isShowCreateModal.value = false

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างประกาศสำเร็จ',
      })
      .then(() => {
        earnPoint.fetch()
      })
  }
)

useWatchTrue(
  () => earnPoint.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(earnPoint.updateStatus.value.errorData),
    })
  }
)

useWatchTrue(
  () => earnPoint.updateStatus.value.isSuccess,
  () => {
    isShowEditModal.value = false
    isActiveItem.value = undefined

    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไขสำเร็จ',
      })
      .then(() => {
        earnPoint.fetch()
      })
  }
)

useWatchTrue(
  () => earnPoint.updateStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(earnPoint.updateStatus.value.errorData),
    })
  }
)

useWatchTrue(
  () => earnPoint.deleteStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ลบสำเร็จ',
      })
      .then(() => {
        earnPoint.fetch()
      })
  }
)

useWatchTrue(
  () => earnPoint.deleteStatus.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(earnPoint.deleteStatus.value.errorData),
    })
  }
)
</script>
