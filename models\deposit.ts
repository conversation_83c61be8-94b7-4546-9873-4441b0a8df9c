import { type IPlayerItem } from '~/models/player'
import { type IBankItem } from '~/models/bank'

export interface IAgentDepositItem {
  id: string
  slip_date: string
  slip_time: string
  created_at: string
  updated_at: string
  player_id: string
  player: IPlayerItem
  agent_id: string
  agent_bank_account_id: string
  agent_bank_account: IBankItem
  type: string
  status: string
  remark: string
  amount: number
  before_amount: number
  after_amount: number
  approved_by: string
  approved_date: any
  rejected_by: string
  rejected_date: any
}
