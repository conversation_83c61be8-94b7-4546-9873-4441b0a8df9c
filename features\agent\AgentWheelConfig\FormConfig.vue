<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <div v-for="(setting, i) in item?.settings || [{}]" :key="setting.wheel_id">
      <h1>#{{ i + 1 }}</h1>
      <FormFields :options="settingFormFields(i).value" class="grid grid-cols-5 gap-4" />
      <hr class="my-4" />
    </div>
    <Button type="submit" class="btn-primary btn-min" :is-loading="status.isLoading">
      บันทึก
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IAgentWebConfigItem } from '~/models/agent'
import { IWheelConfig } from '~/loaders/agentWheel'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item: IWheelConfig
}>()

const transformFormValue = (values: any): Partial<IAgentWebConfigItem> => {
  return {
    ...values,
  }
}

const form = useForm<Partial<IAgentWebConfigItem>>({
  initialValues: transformFormValue(props.item),
})

watch(
  () => props.item,
  (item) => {
    form.setValues(transformFormValue(item))
  }
)

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ค่าเข้าเล่น',
      name: 'price',
      rules: yup.number().min(1).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'จำกัด',
      name: 'limit',
      rules: yup.number().min(1).required(),
    },
  },
])

const settingFormFields = (i: number) =>
  createFormFields(() => [
    {
      type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
      class: 'col-span-2',
      props: {
        label: 'รูปของรางวัล',
        name: `settings[${i}].image`,
        rules: yup.string().nullable(),
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'ชื่อของรางวัล',
        name: `settings[${i}].name`,
        rules: yup.string().required(),
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'มูลค่ารางวัล',
        name: `settings[${i}].amount`,
        rules: yup.number().min(1).required(),
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'น้ำหนักในการออกรางวัล',
        name: `settings[${i}].weight`,
        rules: yup.number().min(0.1).required(),
      },
    },
  ])

const onSubmit = form.handleSubmit((data: any) => {
  emits('submit', {
    ...data,
    price: Number(data.price),
    limit: Number(data.limit),
    settings: ArrayHelper.toArray(data.settings).map((setting: any) => ({
      ...setting,
      weight: Number(setting.weight),
      amount: Number(setting.amount),
    })),
  })
})
</script>
