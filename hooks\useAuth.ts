import { defineStore } from 'pinia'
import { type AxiosRequestConfig, type AxiosResponseHeaders } from 'axios'
import { useObjectLoader } from '~/lib/api/loaderObject'
import { ParamHelper } from '~/utils/ParamHelper'
import { CONFIG } from '~/constants/config'
import { useRequestOptions } from '~/hooks/useRequestOptions'
import { computed } from '#imports'
import { useCookie7Days } from '~/hooks/useCookie'
import { type IMeItem } from '~/models/me'

export interface ILoginPayload {
  username: string
  password: string
}

const useLogin = defineStore('_auth.login', () => {
  const options = useRequestOptions()
  const me = useMe()
  const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)

  return useObjectLoader<IMeItem, ILoginPayload>({
    method: 'post',
    url: '/admin/auth/login',
    getRequestOptions: () => {
      return {
        ...options.getDefault(),
        transformResponse(
          this: AxiosRequestConfig,
          data: any,
          _headers: AxiosResponseHeaders,
          status?: number
        ) {
          if (status && status < 300) {
            const res = JSON.parse(data)

            token.value = res[CONFIG.ACCESS_TOKEN_KEY_NAME]
            me.setData(res)
          }
        },
      }
    },
  })
})

export const useLogout = defineStore('_auth.logout', () => {
  const options = useRequestOptions()
  const me = useMe()

  return useObjectLoader({
    method: 'post',
    url: '/admin/auth/logout',
    getRequestOptions: () => {
      const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)

      return {
        ...options.getDefaultWithAuth(),
        transformResponse(this: AxiosRequestConfig) {
          token.value = null
          me.setData(null)
        },
      }
    },
  })
})

export const useMe = defineStore('_auth.me', () => {
  const options = useRequestOptions()
  const token = useCookie7Days(CONFIG.COOKIE_TOKEN_KEY_NAME)

  return useObjectLoader<IMeItem>({
    method: 'get',
    url: '/admin/auth/me',
    getRequestOptions: () => {
      return {
        ...options.getDefaultWithAuth(),
        transformResponse(this: AxiosRequestConfig, data: any) {
          return {
            ...JSON.parse(data),
            [CONFIG.ACCESS_TOKEN_KEY_NAME]: token.value,
          }
        },
      }
    },
  })
})

export const useAuth = () => {
  const login = useLogin()
  const logout = useLogout()
  const me = useMe()
  const isAuthenticated = computed<boolean>(() => {
    return ParamHelper.getBoolFalse((me.data as any)?.[CONFIG.ACCESS_TOKEN_KEY_NAME])
  })

  const isAgent = computed<boolean>(() => {
    return ParamHelper.getBoolFalse(me.data?.agent_id)
  })

  return {
    login,
    logout,
    me,
    isAuthenticated,
    isAgent,
  }
}
