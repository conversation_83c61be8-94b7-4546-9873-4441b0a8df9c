import { usePageLoader } from '~/lib/api/loaderPage'
import { type IAgentItem } from '~/models/agent'

export const useAgentUserPageLoader = (agentId?: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IAgentItem>({
    baseURL: agentId ? '/admin/users?agent=' + agentId : '/agent/users',
    getBaseRequestOptions: () => getDefaultWithAuth(),
    add: {
      getURL: () => (agentId ? `/admin/agents/${agentId}/users` : '/agent/users'),
    },
    update: {
      getURL: (id) => (agentId ? `/admin/users/${id}` : '/agent/users/' + id),
    },
  })
}
