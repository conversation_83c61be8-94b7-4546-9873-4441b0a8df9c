<template>
  <Form @submit="onSubmit">
    <div class="grid gap-6 grid-cols-2">
      <div>
        <FormFields :options="formFields" />
        <FormFields :options="formFields2" class="grid gap-4 grid-cols-2" />
      </div>
      <div>
        <FormFields :options="formFields3" class="grid gap-4 grid-cols-2" />
      </div>
    </div>
    <Button type="submit" class="btn-primary btn-min" :is-loading="status.isLoading">
      บันทึก
    </Button>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'
import { IAgentWebConfigItem } from '~/models/agent'

const emits = defineEmits<{
  (e: 'submit', values: any): void
}>()

const props = defineProps<{
  status: IStatus
  item: IAgentWebConfigItem
}>()

const transformFormValue = (values: any): Partial<IAgentWebConfigItem> => {
  return {
    ...values,
    keywords: values?.keywords?.split(',') || [],
  }
}

const form = useForm<Partial<IAgentWebConfigItem>>({
  initialValues: transformFormValue(props.item),
})

watch(
  () => props.item,
  (item) => {
    form.setValues(transformFormValue(item))
  }
)

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Title',
      name: 'title',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.TEXT_AREA,
    props: {
      label: 'Description',
      name: 'description',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.TAG,
    props: {
      label: 'Keywords',
      name: 'keywords',
      rules: yup.array(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ไลน์ไอดี',
      name: 'line',
      rules: yup.string(),
    },
  },
])

const formFields2 = createFormFields(() => [
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'โลโก้',
      name: 'logo_url',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'Favicon',
      name: 'favicon_url',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'Cover',
      name: 'cover_url',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      label: 'Background',
      name: 'bg_image_url',
      rules: yup.string(),
    },
  },
])

const formFields3 = createFormFields(() => [
  {
    type: INPUT_TYPES.COLOR_PICKER,
    props: {
      label: 'Primary color',
      name: 'primary_color',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.COLOR_PICKER,
    props: {
      label: 'Secondary color',
      name: 'secondary_color',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.COLOR_PICKER,
    props: {
      label: 'Ternary color',
      name: 'ternary_color',
      rules: yup.string(),
    },
  },
  {
    type: INPUT_TYPES.COLOR_PICKER,
    props: {
      label: 'Background color',
      name: 'bg_color',
      rules: yup.string(),
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  emits('submit', {
    website_config: {
      ...data,
      keywords: ((data.keywords as unknown as any[]) || []).join(','),
    },
  })
})
</script>
