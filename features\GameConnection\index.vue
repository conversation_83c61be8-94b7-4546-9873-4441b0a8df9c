<template>
  <PageHeader :title="routes.connections.name" />
  <Table :options="tableOptions" @pageChange="game.fetch" @search="game.search" />
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import ColumnAction from '~/features/GameConnection/ColumnAction.vue'
import { IGameConnection, useGameConnectionPageLoader } from '~/loaders/gameConnection'

const game = useGameConnectionPageLoader()

game.setFetchLoading()
onMounted(() => {
  game.fetch()
})

const tableOptions = useTable<IGameConnection>({
  repo: game,
  columns: () => [
    {
      value: 'รูป',
    },
    {
      value: 'ชื่อ',
    },
    {
      value: 'สร้างวันที่',
    },
    {
      value: 'แก้ไขล่าสุด',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.name,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: item.updated_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            agent: item,
          },
        },
      ]
    }),
})
</script>
