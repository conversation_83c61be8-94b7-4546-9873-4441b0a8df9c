<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <ProviderSingle />
  </NuxtLayout>
</template>
<script lang="ts" setup>
import { useApp } from '~/hooks/useApp'
import { routes } from '~/constants/routes'
import { LAYOUTS } from '~/constants/layouts'
import { definePageMeta } from '#imports'
import { MIDDLEWARES } from '~/constants/middlewares'
import ProviderSingle from '~/features/ProviderSingle/index.vue'
import { useProviderPageStore } from '~/loaders/provider'

const app = useApp()
const route = useRoute()
const provider = useProviderPageStore()

definePageMeta({
  middleware: [
    MIDDLEWARES.AUTH_ADMIN,
    async (to) => {
      const provider = useProviderPageStore()

      await provider.find(to.params.id as string)

      if (ParamHelper.isCodeNotFoundError(provider.findOptions)) {
        return abortNavigation({
          statusCode: 404,
        })
      }
    },
  ],
})

const nav = routes.providerEdit(route.params.id as string, provider.findItem?.name)

app.updateDocMeta({ title: nav.name })
app.updatePageMeta({
  title: nav.name,
  breadcrumbs: [routes.home, routes.providers, nav],
})
</script>
