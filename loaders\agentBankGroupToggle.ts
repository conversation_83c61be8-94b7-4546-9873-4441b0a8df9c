import { useObjectLoader } from '~/lib/api/loaderObject'
import { type IBankItem } from '~/models/bank'

export const useAgentBankGroupToggle = () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IBankItem>({
    method: 'PUT',
    url: 'agent/banks/:id/toggle-limit',
    getURL: (data) => `agent/group-banks/${data.id}/toggle-bank`,
    getRequestOptions: () => getDefaultWithAuth(),
  })
}
