@layer components {
  .sidebar {
    @apply fixed z-30 inset-y-0 left-0 w-64 transition duration-300 transform bg-gray-800 overflow-y-auto lg:translate-x-0 lg:static lg:inset-0 ;

    .sidebar-item {
      &.router-link-active {
        @apply bg-gray-900 text-light hover:bg-gray-700 hover:text-light #{!important};
      }
    }

    .sidebar-item-root {
      &.router-link-exact-active {
        @apply bg-gray-900 text-light hover:bg-gray-700 hover:text-light #{!important};
      }

      &.router-link-active {
        @apply text-gray-300 hover:bg-gray-700 hover:text-white;
      }
    }
  }
}
