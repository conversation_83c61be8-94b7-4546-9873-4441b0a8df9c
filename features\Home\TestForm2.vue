<template>
  <Fields :options="formFields2" :form="form2" />
</template>
<script lang="ts" setup>
import { FormContext } from 'vee-validate'
import * as yup from 'yup'
import { inject } from '#imports'
import { INPUT_TYPES } from '~/components/Form/types'
import Fields from '~/components/Form/Fields.vue'

const form2 = inject<
  FormContext<{
    email2: string
    password2: string
  }>
>('form2')

const formFields2 = createFormFields(() => [
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'password',
      name: 'password2',
      placeholder: 'password',
      rules: yup.string().required(),
    },
  },
])
</script>
