<template>
  <div>
    <Modal
      v-model="isShowAddCreditModal"
      class="modal-md"
      :no-backdrop-close="true"
      title="เพิ่มเครดิต"
    >
      <CreditForm :status="creditPlus.status.value" @submit="creditPlus.run" />
    </Modal>
    <Modal
      v-model="isShowRemoveCreditModal"
      class="modal-md"
      :no-backdrop-close="true"
      title="ลบเครดิต"
    >
      <CreditForm :status="creditMinus.status.value" @submit="creditMinus.run" />
    </Modal>
    <div class="flex justify-between items-center">
      <PageHeader :title="`รายละเอียด ${agent.findItem?.name}`" />
      <div class="space-x-4">
        <Button class="btn-warning" @click="isShowAddCreditModal = true">เพิ่มเครดิต</Button>
        <Button class="btn-warning" @click="isShowRemoveCreditModal = true">ลบเครดิต</Button>
        <Button
          :class="{
            'btn-danger': agent.findItem?.is_enabled,
            'btn-success': !agent.findItem?.is_enabled,
          }"
          :is-loading="agentEnabled.status.value.isLoading"
          @click="onToggleEnabled"
        >
          {{ agent.findItem?.is_enabled ? 'ปิดการใช้งาน' : 'เปิดการใช้งาน' }}
        </Button>
      </div>
    </div>
    <div class="card p-6">
      <DetailForm :status="agent.updateStatus" :item="agent.findItem" @submit="onUpdateSubmit" />
    </div>
    <div class="mt-8">
      <Users />
    </div>
    <div class="mt-8">
      <Providers />
    </div>
  </div>
</template>
<script lang="tsx" setup>
import {
  useAgentCreditMinus,
  useAgentCreditPlus,
  useAgentEnabledUpdate,
  useAgentPageStore,
} from '~/loaders/agent'
import DetailForm from '~/features/AgentSingle/DetailForm.vue'
import CreditForm from '~/features/AgentSingle/CreditForm.vue'
import Users from '~/features/AgentSingle/Users.vue'
import Providers from '~/features/AgentSingle/Providers.vue'

const agent = useAgentPageStore()
const route = useRoute()
const dialog = useDialog()
const creditPlus = useAgentCreditPlus(route.params.id as string, {
  isShowDialog: true,
  onBeforeSuccess: () => {
    isShowAddCreditModal.value = false
  },
})

const creditMinus = useAgentCreditMinus(route.params.id as string, {
  isShowDialog: true,
  onBeforeSuccess: () => {
    isShowRemoveCreditModal.value = false
  },
})

const agentEnabled = useAgentEnabledUpdate(route.params.id as string, {
  isShowDialog: true,
})

const isShowAddCreditModal = ref(false)
const isShowRemoveCreditModal = ref(false)

useWatchTrue(
  () => agent.updateStatus.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'แก้ไข Agent สำเร็จ',
      })
      .then(() => {
        agent.find(route.params.id as string)
      })
  }
)

useWatchTrue(
  () => agent.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(agent.updateStatus.errorData),
    })
  }
)

const onToggleEnabled = () => {
  dialog
    .warning({
      title: agent.findItem?.is_enabled
        ? 'คุณต้องการปิดการใช้งาน Agent นี้ใช่หรือไม่?'
        : 'คุณต้องการเปิดการใช้งาน Agent นี้ใช่หรือไม่?',
      message: agent.findItem?.is_enabled
        ? 'Agent นี้จะไม่สามารถใช้งานได้'
        : 'Agent นี้จะสามารถใช้งานได้',
      confirmText: agent.findItem?.is_enabled ? 'ปิดการใช้งาน' : 'เปิดการใช้งาน',
      cancelText: 'ยกเลิก',
      isShowCancelBtn: true,
    })
    .then(() => {
      agentEnabled.run({
        is_enabled: !ParamHelper.getBoolFalse(agent.findItem?.is_enabled),
      })
    })
}

const onUpdateSubmit = (values: any) => {
  agent.update(route.params.id as string, values)
}
</script>
