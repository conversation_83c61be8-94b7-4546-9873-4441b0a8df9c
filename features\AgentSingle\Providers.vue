<template>
  <div>
    <Modal v-model="isShowCreateModal" title="เพิ่มค่ายเกม" class="modal-md">
      <ProvidersCreateForm :status="provider.addStatus.value" @submit="provider.add" />
    </Modal>
    <div class="flex items-center justify-between">
      <PageHeader title="ค่ายเกม" />
      <Button class="btn-primary btn-min" @click="isShowCreateModal = true">เพิ่ม</Button>
    </div>
    <Table :options="providerTableOptions" @pageChange="provider.fetch" @search="provider.search" />
  </div>
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { IAgentProviderItem } from '~/models/provider'
import ProvidersCreateForm from '~/features/AgentSingle/ProvidersCreateForm.vue'
import ProvidersColumnAction from '~/features/AgentSingle/ProvidersColumnAction.vue'

const route = useRoute()
const isShowCreateModal = ref(false)
const provider = useAgentProviderPageLoader(route.params.id as string)
const dialog = useDialog()

provider.setFetchLoading()
onMounted(() => {
  provider.fetch()
})

const providerTableOptions = useTable<IAgentProviderItem>({
  repo: provider,
  columns: () => [
    {
      value: 'รูป',
    },
    {
      value: 'ชื่อ',
    },
    {
      value: 'เปอเซนต์',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.provider.name,
        },
        {
          value: item.percent,
        },
        {
          value: item.is_enabled,
          type: COLUMN_TYPES.BOOLEAN,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ProvidersColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            provider: item,
          },
          on: {
            reload: () => {
              provider.fetch()
            },
          },
        },
      ]
    }),
})

useWatchTrue(
  () => provider.addStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'เพิ่มค่ายเกมสำเร็จ',
      })
      .then(() => {
        isShowCreateModal.value = false
        provider.fetch()
      })
  }
)

useWatchTrue(
  () => provider.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(provider.addStatus.value.errorData),
    })
  }
)
</script>
