<template>
  <div>
    <Menu as="div" class="relative inline-block text-left">
      <div>
        <MenuButton as="div">
          <Button :is-only-icon="true" class="btn-info" icon="gear-solid" />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute right-0 mt-2 z-20 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        >
          <div class="px-1 py-1">
            <nuxt-link
              :href="routes.providerEdit(item.props.agent.id).to"
              :class="[
                'group flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 hover:bg-primary hover:text-white',
              ]"
            >
              แก้ไข
            </nuxt-link>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>
<script lang="tsx" setup>
import { Menu, MenuButton, MenuItems } from '@headlessui/vue'
import { PropType } from 'vue'
import { IRowItem } from '~/components/Table/types'
import { IProviderItem } from '~/models/provider'

defineProps({
  item: {
    type: Object as PropType<IRowItem<{ agent: IProviderItem }>>,
    required: true,
  },
})
</script>
