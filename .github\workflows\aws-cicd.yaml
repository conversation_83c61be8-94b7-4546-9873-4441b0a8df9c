name: Admin Frontend CI/CD

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch: {}

permissions:
  id-token: write
  contents: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  admin-frontend-cicd:
    uses: onecorp-tech/onecorp-infra-k8s/.github/workflows/shared-ci.yaml@main
    secrets:
      token: ${{ secrets.GH_PAT }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
    with:
      service: admin-frontend
      environment: ${{ github.ref_name == 'main' && 'winkplus-prod' || 'winkplus-dev' }}
      ecr_repository: winkplus
      docker_build_file: Dockerfile
      docker_build_dir: .
      docker_build_platform: linux/arm64
      SLACK_CHANNEL_ID: C08K0FJEJNS
      docker_build_args: |
        HOST=0.0.0.0
        PORT=3000
        APP_BASE_API=https://api.wiwp.dev
        APP_BASE_API_MOCK=https://admin.wiwp.dev
