@layer components {
  .switch-control {
    @apply relative  inline-flex h-[35px] w-[65px] shrink-0 cursor-pointer rounded-full border-4
    border-transparent transition-colors duration-200 ease-in-out focus:outline-none
    focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 group-focus:ring-primary;
    @apply disabled:opacity-40 disabled:cursor-auto #{!important};

    &.active {
      @apply bg-white border-primary;
    }

    &.inactive {
      @apply bg-white border-gray-300;
    }

    .switch-control-inner {
      @apply pointer-events-none inline-block mt-1 h-[20px] w-[20px] transform rounded-full
      bg-white shadow-lg ring-0 transition duration-200 ease-in-out;
    }
  }
}
