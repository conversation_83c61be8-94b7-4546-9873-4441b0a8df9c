import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IGameSummaryItem, type IGameTransactionItem } from '~/models/game'
import { useRequestOptions } from '~/hooks/useRequestOptions'

export const useAgentGameTransactionPageLoader = defineStore('agent.game_transactions.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IGameTransactionItem>({
    baseURL: 'agent/game-transactions',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentGameTransactionByPlayerPageLoader = defineStore(
  'agent.game_transactions_player.page',
  () => {
    const { getDefaultWithAuth } = useRequestOptions()
    const route = useRoute()

    return usePageLoader<IGameTransactionItem>({
      baseURL: 'agent/game-transactions?player=' + route.params.id,
      getBaseRequestOptions: () => {
        return {
          ...getDefaultWithAuth(),
          params: {
            limit: 10,
          },
        }
      },
    })
  }
)

export const useAgentGameSummaryPageLoader = defineStore('agent.game_summary.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IGameSummaryItem>({
    baseURL: 'agent/game-summary',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})
