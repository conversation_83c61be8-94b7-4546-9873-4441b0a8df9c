<template>
  <Table :options="tableOptions" @pageChange="redeem.fetch" @search="redeem.search" />
</template>
<script lang="tsx" setup>
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { type IRedeemHistoryItem } from '~/models/earnPoint'
import { useAgentRedeemHistoryPageLoader } from '~/loaders/agentEarnpoint'
import ColumnAction from '~/containers/AgentRedeemHistoryTable/ColumnAction.vue'

const props = defineProps<{
  redeem: useAgentRedeemHistoryPageLoader
}>()

props.redeem.setFetchLoading()
onMounted(() => {
  props.redeem.fetch()
})

const tableOptions = useTable<IRedeemHistoryItem>({
  options: {
    isNotChangeRoute: true,
  },
  repo: props.redeem,
  columns: () => [
    {
      value: 'ผู้เล่น',
    },
    {
      value: 'รูป',
    },
    {
      value: 'หัวข้อ',
    },
    {
      value: 'point',
    },
    {
      value: 'credit fee',
    },
    {
      value: 'status',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.player,
          type: COLUMN_TYPES.PLAYER,
        },
        {
          value: item.reward.image_url,
          type: COLUMN_TYPES.IMAGE,
        },
        {
          value: item.reward.title,
        },
        {
          value: item.reward.point,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.reward.credit,
          type: COLUMN_TYPES.NUMBER,
        },
        {
          value: item.status,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            item,
            onDone: () => {
              props.redeem.fetch()
            },
          },
        },
      ]
    }),
})
</script>
