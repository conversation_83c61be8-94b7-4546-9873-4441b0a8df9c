<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <div class="container mx-auto px-4">
      <section>
        <PageHeader title="Table" />
        <TableCustom :options="tableOptions" @pageChange="handlePageChange">
          <template #toolbar> Toolbar </template>
          <template #topPaginationAction>
            <div class="text-base font-medium cursor-pointer">
              <em class="ic ic-trash-solid text-lg mr-1" />
              ลบรายการที่เลือก
            </div>
          </template>
        </TableCustom>
      </section>
      <section>
        <PageHeader title="Simple Table" />
        <SimpleTable :options="simpleTableOptions" />
      </section>
    </div>
  </NuxtLayout>
</template>

<script lang="tsx" setup>
import { onMounted } from '#imports'
import { LAYOUTS } from '~/constants/layouts'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'
import { ITableItem, useSimpleTableStore, useTableStore } from '~/loaders/mock/useTableLoader'
import { useSimpleTable, useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'

import TableCustom from '~/components/Table/index.vue'
import SimpleTable from '~/components/Table/SimpleTable.vue'
import Button from '~/components/Button.vue'

const app = useApp()
const tableStore = useTableStore()
const simpleTableStore = useSimpleTableStore()
const route = useRoute()

const currentPage = computed(() => Number(route.query.page) || 1)

app.updateDocMeta({ title: styleguideMenu.table.name })
app.updatePageMeta({
  title: styleguideMenu.table.name,
  breadcrumbs: [styleguideMenu.table],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))

tableStore.setFetchLoading()
simpleTableStore.setLoading()
onMounted(() => {
  tableStore.fetch(currentPage.value, undefined, { params: { limit: 3 } })
  simpleTableStore.run({})
})

const state = reactive<{ change: boolean }>({
  change: true,
})

const tableOptions = useTable<ITableItem>({
  repo: tableStore,
  columns: () => [
    {
      value: state.change ? 'ชื่อ - นามสกุล' : 'ชื่อ',
    },
    {
      value: 'อีเมล',
    },
    {
      value: '',
    },
    {
      value: 'แก้ไข',
      className: 'text-center',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: `<span class="font-bold">Mr.</span> ${state.change ? item.name : 'Test'}`,
          className: 'font-light',
        },
        {
          value: item.email,
        },
        {
          type: COLUMN_TYPES.COMPONENT,
          value: defineComponent(() => {
            return () => (
              <Button class="btn-primary" onClick={test}>
                Change
              </Button>
            )
          }),
        },
        {
          type: COLUMN_TYPES.ACTION,
          value: '',
          props: {
            deleteDialogItem: {
              title: 'ยืนยันการลบ',
              message: `คุณต้องการลบรายการ ${item.name} ใช่หรือไม่`,
              confirmText: 'ลบ',
            },
          },
          on: {
            delete: (value: string) => {
              window.console.log('delete', value)
            },
          },
        },
      ]
    }),
  options: {
    isShowCheckbox: true,
    isHideToolbar: true,
    isNotChangeRoute: false,
    onCheckBoxClick: (index: number[]) => {
      window.console.log('rowsIndex', index)
    },
  },
})

const simpleTableOptions = useSimpleTable({
  items: () => simpleTableStore.items,
  status: () => simpleTableStore.status,
  columns: () => [
    {
      value: 'ชื่อ - นามสกุล',
    },
    {
      value: 'อีเมล',
    },
    {
      value: 'แก้ไข',
      className: 'text-center',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.name,
        },
        {
          value: item.email,
        },
        {
          type: COLUMN_TYPES.ACTION,
          value: '',
        },
      ]
    }),
})

const test = () => {
  state.change = !state.change
}

const handlePageChange = (page: number) => {
  tableStore.fetch(page, undefined, { params: { limit: 3 } })
}
</script>
