export interface IPaymentItem {
  id: string
  created_at: string
  updated_at: string
  agent_id: string
  type: string
  name: string
  agent: any
  settings: {
    merchant_code: string
    api_key: string
    min_deposit: number
    max_deposit: number
    min_withdraw: number
    max_withdraw: number
  }

  sort_order: number
  is_deposit_auto: boolean
  is_withdraw_auto: boolean
  is_enabled: boolean
}
