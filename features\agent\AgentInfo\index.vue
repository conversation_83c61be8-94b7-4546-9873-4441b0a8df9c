<template>
  <Loader :is-loading="agent.status.value.isLoading">
    <PageHeader :title="`ข้อมูล ${agent.data.value?.name}`" />
    <div class="card p-6">
      <Form :item="agent.data.value" />
    </div>
  </Loader>
</template>
<script lang="tsx" setup>
import Form from '~/features/agent/AgentInfo/Form.vue'
import { useAgentConfigUpdateLoader } from '~/loaders/agent'

const agent = useAgentLoader()
const agentConfig = useAgentConfigUpdateLoader()
const dialog = useDialog()

agent.setLoading()
onMounted(() => {
  agent.run()
})

useWatchTrue(
  () => agentConfig.status.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'ปรับแต่งเว็บไซต์สำเร็จ',
      })
      .then(() => {
        agent.run()
      })
  }
)

useWatchTrue(
  () => agentConfig.status.value.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(agentConfig.status.value.errorData),
    })
  }
)
</script>
