<template>
  <FormFields :options="formFields" />
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IAgentItem } from '~/models/agent'

const props = defineProps<{
  item: IAgentItem
}>()

const form = useForm<
  Partial<{
    name: string
    prefix: string
    domain: string
    credit: number
  }>
>({
  initialValues: props.item,
})

watch(
  () => props.item,
  (item) => {
    form.setValues(item)
  }
)

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.STATIC,
    props: {
      label: 'ชื่อ',
      name: 'name',
    },
  },
  {
    type: INPUT_TYPES.STATIC,
    props: {
      label: 'Prefix',
      name: 'prefix',
    },
  },
  {
    type: INPUT_TYPES.STATIC,
    props: {
      label: 'โดเมน',
      name: 'domain',
    },
  },
  {
    type: INPUT_TYPES.STATIC,
    props: {
      label: 'เครดิต',
      name: 'credit',
    },
  },
])
</script>
