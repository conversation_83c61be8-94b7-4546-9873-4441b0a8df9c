import {
  type IFieldProps,
  type IFormFieldBase,
  type INPUT_TYPES,
  type IOption,
} from '~/components/Form/types'

export interface ISelectAutoCompleteFieldProps extends IFieldProps {
  options?: IOption[]
  initOptions?: IOption[]
  notFoundText?: string
  isLoading?: boolean
}

export type ISelectAutoCompleteField = IFormFieldBase<
  INPUT_TYPES.SELECT_AUTO_COMPLETE,
  ISelectAutoCompleteFieldProps,
  {
    change?: (value: string) => void
    search?: (value: string) => void
  }
>
