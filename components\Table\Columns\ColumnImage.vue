<template>
  <div class="space-x-2 space-y-2">
    <Image
      v-for="v in getValue"
      :key="v"
      :src="v"
      :alt="v"
      class="max-h-[40px] max-w-[60px] rounded ml-2"
    />
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue'
import { IRowItem } from '../types'
import { computed } from '#imports'

const props = defineProps({
  item: {
    type: Object as PropType<IRowItem<{ max?: number }>>,
    required: true,
  },
})

const getValue = computed<string[]>(() => {
  return typeof props.item?.value === 'string'
    ? [props.item?.value]
    : ArrayHelper.toArray(props.item?.value || [])
})
</script>
