<template>
  <div>
    <Modal v-model="isShowCreateUserModal" title="สร้างผู้ใช้งาน" class="modal-md">
      <CreateForm
        :status="user.addStatus.value"
        :agent="auth.me.data!.agent!.prefix"
        @submit="user.add"
      />
    </Modal>
    <div class="flex items-center justify-between">
      <PageHeader title="ผู้ใช้งาน" />
      <Button class="btn-primary btn-min" @click="isShowCreateUserModal = true">สร้าง</Button>
    </div>
    <Table :options="userTableOptions" @pageChange="user.fetch" @search="user.search" />
  </div>
</template>
<script lang="ts" setup>
import CreateForm from './CreateForm.vue'
import ColumnAction from './ColumnAction.vue'
import { useTable } from '~/hooks/useTable'
import { IUserItem } from '~/models/user'
import { COLUMN_TYPES } from '~/components/Table/types'

const isShowCreateUserModal = ref(false)
const user = useAgentUserPageLoader()
const dialog = useDialog()
const auth = useAuth()

user.setFetchLoading()
onMounted(() => {
  user.fetch()
})

const userTableOptions = useTable<IUserItem>({
  repo: user,
  columns: () => [
    {
      value: 'ล๊อกอินล่าสุด',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: 'username',
    },
    {
      value: 'ชื่อ',
    },

    {
      value: 'IP',
    },
    {
      value: 'ตำแหน่ง',
    },
    {
      value: 'สถานะ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.last_active,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: item.username,
        },
        {
          value: item.full_name,
        },
        {
          value: item.last_ip,
        },
        {
          value: item.role,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.is_enabled,
          type: COLUMN_TYPES.BOOLEAN,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            user: item,
          },
          on: {
            reload: () => {
              user.fetch()
            },
          },
        },
      ]
    }),
})

useWatchTrue(
  () => user.addStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างผู้ใช้งานสำเร็จ',
      })
      .then(() => {
        isShowCreateUserModal.value = false
        user.fetch()
      })
  }
)

useWatchTrue(
  () => user.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(user.addStatus.value.errorData),
    })
  }
)
</script>
