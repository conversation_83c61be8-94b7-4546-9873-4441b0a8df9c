<template>
  <div class="flex h-screen bg-admin relative">
    <div
      :class="sidebarOpen ? 'block' : 'hidden'"
      class="fixed z-20 inset-0 bg-black opacity-50 transition-opacity lg:hidden"
      @click="sidebarOpen = false"
    />
    <Sidebar :sidebar-open="sidebarOpen" />
    <div class="flex-1 flex flex-col overflow-hidden">
      <Navbar
        :sidebar-open="sidebarOpen"
        :set-sidebar="(bool) => (sidebarOpen = bool)"
        :is-admin="true"
      />
      <main class="flex-1 overflow-x-hidden overflow-y-auto pb-[80px] lg:pb-0">
        <div class="mx-auto px-4 md:px-6 py-6">
          <Breadcrumbs v-if="!pageMeta.isHideBreadcrumbs" />
          <slot />
        </div>
      </main>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Breadcrumbs from './components/Breadcrumbs.vue'
import { ref } from '#imports'
import Sidebar from '~/layouts/components/Sidebar.vue'
import Navbar from '~/layouts/components/Navbar.vue'

const app = useApp()
const pageMeta = computed(() => app.pageMeta)
const sidebarOpen = ref(false)
</script>
