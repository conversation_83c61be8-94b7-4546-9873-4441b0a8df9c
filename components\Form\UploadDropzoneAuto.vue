<template>
  <FieldWrapper v-bind="wrapperProps">
    <div
      class="dropzone"
      :class="[{ 'bg-gray-fill': isDragover }, dropzoneClassName, fieldClassName]"
      @dragover="dragover"
      @dragleave="dragleave"
      @drop="drop"
    >
      <em
        v-if="value"
        title="delete"
        class="ic ic-trash-solid close-icon"
        @click="handleDeleteFile"
      />
      <input
        ref="fileInput"
        type="file"
        :accept="acceptFile"
        :disabled="disabled"
        @change="handleChange"
      />
      <div class="dropzone-wrapper">
        <div v-if="value" class="dropzone-preview">
          <div class="preview-img">
            <img :src="value" alt="file" />
          </div>
          <div v-if="innerValue" class="preview-file-name">
            <p>{{ innerValue.name }}</p>
          </div>
          <div v-if="innerValue" class="preview-file-info">{{ selectedFileSize + ' MB' }}</div>
        </div>
        <Progressbar v-if="upload.status.value.isLoading" class="w-3/4 mb-8" :percent="percent" />
        <Button
          v-if="!innerValue"
          class="btn-primary text-sm min-w-[150px]"
          @click="handleOpenFile"
        >
          {{ value ? 'เลือกไฟล์ใหม่' : 'เลือกไฟล์' }}
        </Button>
        <div v-if="!innerValue" class="dropzone-message">
          <p>{{ placeholder || 'หรือลากวางไฟล์ของคุณในกล่องนี้' }}</p>
          <p v-if="acceptFileSize">ขนาดไม่เกิน: {{ (acceptFileSize / 1000).toFixed(2) }} MB</p>
        </div>
      </div>
    </div>
  </FieldWrapper>
</template>

<script lang="tsx" setup>
import { useFieldHOC } from '~/hooks/useForm'
import FieldWrapper from '~/components/Form/FieldWrapper.vue'
import { computed } from '#imports'
import { IUploadDropzoneAutoProps } from '~/components/Form/upload_dropzone_auto.types'
import { useUploadLoader } from '~/loaders/useUploadLoader'

const props = withDefaults(defineProps<IUploadDropzoneAutoProps>(), {})
const emit = defineEmits(['change', 'delete'])

const {
  wrapperProps,
  fieldClassName,
  disabled,
  handleChange: onChange,
  setErrors,
  value,
} = useFieldHOC<string>(props)

const upload = useUploadLoader()
const fileInput = ref<HTMLInputElement>()
const isDragging = ref<boolean>(false)
const isDragover = ref<boolean>(false)
const innerValue = ref<File | undefined>(undefined)
const percent = ref<number>(0)

const acceptFile = computed(() =>
  typeof props.accept === 'string' ? props.accept : props.accept?.join(',')
)

const acceptFileSize = computed(() => props.maxSize)
const selectedFileSize = computed(() => ((innerValue.value?.size || 0) / 1000 / 1000).toFixed(2))

const handleChange = (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0]
  const result = handleCheckFileCondition(file)

  innerValue.value = file

  if (result) {
    const formData = new FormData()

    formData.append('file', file as any)
    upload.run(formData, { data: { onUploadProgress, onDownloadProgress } })
  }
}

useWatchTrue(
  () => upload.status.value.isSuccess,
  () => {
    onChange(upload.data.value!.url)
    emit('change', upload.data.value!.url)
    percent.value = 0
  }
)

useWatchTrue(
  () => upload.status.value.isError,
  () => {
    setErrors(StringHelper.getError(upload.status.value.errorData))
    percent.value = 0
  }
)

const handleOpenFile = () => {
  fileInput.value?.click()
}

const handleDeleteFile = () => {
  fileInput.value?.value && (fileInput.value.value = '')
  innerValue.value = undefined
  onChange(undefined)
  emit('delete')
}

const handleCheckFileCondition = (file: File | undefined): boolean => {
  if (!file) return false
  const accept = checkAcceptFile(file)

  if (!accept) {
    setErrors('File type is not supported')

    return false
  }

  const maxSize = checkMaxSize(file)

  if (!maxSize) {
    setErrors('File size is too large')

    return false
  }

  setErrors('')

  return true
}

const dragover = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
  isDragover.value = true
}

const dragleave = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  isDragover.value = false
}

const drop = (e: DragEvent) => {
  e.preventDefault()
  const file = e.dataTransfer?.files[0]
  const result = handleCheckFileCondition(file)

  isDragging.value = false
  isDragover.value = false

  if (result) {
    emit('change', file)
  }
}

const checkAcceptFile = (file: File): boolean => {
  const fileType = file.type.split('/')[1]

  return acceptFile.value ? acceptFile.value.includes(fileType) : true
}

const checkMaxSize = (file: File): boolean => {
  if (acceptFileSize.value) {
    return file.size / 1000 <= acceptFileSize.value
  }

  return true
}

const onUploadProgress = (progressEvent: ProgressEvent) => {
  percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.8
}

const onDownloadProgress = (progressEvent: ProgressEvent) => {
  if (progressEvent.total === 0) {
    percent.value = 100

    return
  }

  percent.value = (Math.floor((progressEvent.loaded * 100) / progressEvent.total) || 0) * 0.2 + 80
}
</script>
