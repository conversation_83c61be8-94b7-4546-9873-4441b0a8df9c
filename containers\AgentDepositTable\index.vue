<template>
  <Table :options="tableOptions" @pageChange="onFetch" @search="onSearch">
    <template #toolbar>
      <div>
        <FormFields :form="form" :options="formFields" class="w-[250px]" />
      </div>
    </template>
  </Table>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import ColumnAction from './ColumnAction.vue'
import { useTable } from '~/hooks/useTable'
import { COLUMN_TYPES } from '~/components/Table/types'
import { IAgentDepositItem } from '~/models/deposit'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { useAgentDepositPageLoader } from '~/loaders/agentDeposit'

const props = defineProps<{
  deposit: useAgentDepositPageLoader
}>()

const form = useForm<
  Partial<{
    status: string
  }>
>({
  initialValues: {
    status: '',
  },
})

props.deposit.setFetchLoading()
onMounted(() => {
  props.deposit.fetch()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'status',
      placeholder: 'สถานะ',
      label: '',
      rules: yup.string(),
      options: [
        {
          label: 'ทั้งหมด',
          value: '',
        },
        {
          label: 'pending',
          value: 'pending',
        },
        {
          label: 'rejected',
          value: 'rejected',
        },
        {
          label: 'approved',
          value: 'approved',
        },
        {
          label: 'success',
          value: 'success',
        },
      ],
    },
  },
])

const tableOptions = useTable<IAgentDepositItem>({
  options: {
    isNotChangeRoute: true,
  },
  repo: props.deposit,
  columns: () => [
    {
      value: '#',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'ผู้ฝาก',
    },
    {
      value: 'บัญชีโอน',
    },
    {
      value: 'บัญชีรับ',
    },
    {
      value: 'จำนวนเครดิต',
      className: 'text-right',
    },
    {
      value: 'คงเหลือ (ก่อน-หลัง)',
      className: 'text-right',
    },
    {
      value: 'เวลาฝาก (สลิป)',
    },
    {
      value: 'เวลาฝาก',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.id,
          props: {
            max: 11,
          },
          className: 'text-xs',
        },
        {
          value: item.status,
          type: COLUMN_TYPES.STATUS,
        },
        {
          value: item.player ? item.player : '-',
          type: item.player ? COLUMN_TYPES.PLAYER : COLUMN_TYPES.VALUE,
        },
        {
          value: item.player?.bank
            ? {
                image_url: item.player.bank?.image_url,
                name: item.player.first_name + ' ' + item.player.last_name,
                number: item.player.bank_number,
                bank: item.player.bank?.name_th,
              }
            : '-',
          type: item.player?.bank ? COLUMN_TYPES.BANK : COLUMN_TYPES.VALUE,
        },
        {
          value: {
            image_url: item.agent_bank_account?.bank?.image_url,
            name: item.agent_bank_account?.account_name,
            number: item.agent_bank_account?.account_number,
            bank: item.agent_bank_account?.bank?.name_th,
            type: item.type,
          },
          type: COLUMN_TYPES.BANK,
        },
        {
          value: item.amount,
          type: COLUMN_TYPES.NUMBER,
          className: 'text-right',
        },
        {
          value:
            item.status === 'approved'
              ? `${StringHelper.withComma(item.before_amount)} - ${StringHelper.withComma(
                  item.after_amount
                )}`
              : '-',
          className: 'text-right',
        },
        {
          value: item.slip_date,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: ColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            item,
            onDone: () => {
              props.deposit.fetch()
            },
          },
        },
      ]
    }),
})

const onFetch = (page = 1, search = props.deposit.fetchOptions.search) => {
  props.deposit.fetch(page, search, {
    params: {
      status: form.values.status,
    },
  })
}

const onSearch = (search: string) => {
  onFetch(1, search)
}

watch(
  () => form.values.status,
  () => {
    onFetch()
  }
)
</script>
