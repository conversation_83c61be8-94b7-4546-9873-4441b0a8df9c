<template>
  <NuxtLayout :name="LAYOUTS.ADMIN">
    <PageHeader title="Modal (Dialog)" />
    <div class="text-description mb-6">วิธีการใช้ Modal</div>

    <Modal v-model="showModal" :class="customClass" title="เปลี่ยนรหัสผ่านของบัญชี">
      <p class="text-center font-normal text-xl text-gray-400">
        เมื่อทำการเปลี่ยนรหัสผ่านแล้ว<br />
        สามารถขอรหัสผ่านชุดใหม่ได้ภายหลัง
      </p>
    </Modal>

    <Modal v-model="showModalLong" class="modal-sm" title="Modal of long content">
      <p class="text-base indent-6">
        Sesame snaps lollipop cotton candy ice cream tart jelly beans cheesecake. Icing candy apple
        pie marshmallow danish shortbread bear claw tart. Cupcake macaroon sugar plum icing carrot
        cake wafer jelly beans dessert. Powder liquorice marshmallow candy chocolate cake
        marshmallow. Toffee topping wafer pudding pie chocolate cake. Tart marzipan tiramisu donut
        candy ice cream. Pie liquorice muffin marshmallow marzipan. Pastry marzipan pudding cookie
        chocolate oat cake cupcake. Pudding brownie dessert marshmallow chocolate. Apple pie icing
        soufflé chocolate biscuit chocolate jelly bonbon tiramisu. Oat cake donut gummies shortbread
        dragée gingerbread ice cream cake topping. Halvah tiramisu dragée lemon drops sweet tiramisu
        sesame snaps sweet. Bear claw pudding chocolate bar gummi bears dragée. Gummies pastry
        jujubes jujubes liquorice biscuit carrot cake jelly.
      </p>
      <p class="text-base indent-6">
        Lemon drops muffin jelly tootsie roll soufflé gummi bears tart. Macaroon oat cake jujubes
        jelly apple pie. Oat cake cheesecake apple pie gingerbread pie. Sugar plum pudding macaroon
        lollipop chocolate bar sweet. Bonbon gingerbread soufflé powder toffee. Muffin liquorice
        brownie icing lollipop croissant. Sugar plum fruitcake donut sugar plum shortbread marzipan
        cake toffee soufflé. Candy canes cookie sweet roll tootsie roll chocolate cake sweet roll.
        Dessert cotton candy tart bear claw caramels caramels dessert. Lollipop icing ice cream
        lollipop tiramisu biscuit marzipan. Lemon drops danish sugar plum carrot cake icing. Candy
        canes oat cake pastry icing gummi bears. Icing sweet muffin toffee sweet roll topping.
      </p>
      <p class="text-base indent-6">
        Marshmallow tiramisu chocolate bar gingerbread apple pie. Cookie topping gummies croissant
        tart icing cake caramels. Gingerbread liquorice icing jelly-o sweet. Sugar plum carrot cake
        sugar plum caramels donut gummies pie sugar plum. Liquorice biscuit lemon drops topping
        candy canes. Chocolate bar chocolate cake dragée cake pie cookie topping. Lemon drops
        jelly-o danish bonbon chupa chups croissant tart. Pastry croissant wafer jelly chocolate
        cake dessert cotton candy croissant lemon drops. Jelly beans sugar plum powder macaroon
        wafer tart lollipop. Macaroon caramels muffin sugar plum tart liquorice. Biscuit marzipan
        shortbread candy canes apple pie tart cake jelly beans. Jelly beans gingerbread wafer chupa
        chups sesame snaps. Jelly lollipop cupcake dragée soufflé dessert. Chupa chups cake chupa
        chups wafer icing dragée.
      </p>
    </Modal>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Live Demo</h1>
      <Button class="block mb-2" @click="onOpenDialog('modal-sm')"> Open Small Modal </Button>
      <Button class="block mb-2" @click="onOpenDialog('modal-md')"> Open Medium Modal </Button>
      <Button class="block mb-2" @click="onOpenDialog('modal-lg')"> Open Large Modal </Button>
      <Button class="block mb-2" @click="onOpenDialog('modal-lg modal-primary-header')">
        Open Modal with Style Header
      </Button>
      <Button class="block mb-2" @click="showModalLong = true">
        Open Modal (เนื้อหายาววววววววววว)
      </Button>
    </div>

    <div class="bg-white w-full p-8 rounded-10 drop-shadow-md mb-4">
      <h1 class="display-heading mb-2">Modal Option</h1>
      <pre
        class="p-4 bg-gray-fill mb-4"
      ><code>&lt;Modal v-model="showModal"...&gt; Content here &lt;/Modal&gt;</code></pre>
      <h3 class="text-lg">title</h3>
      <span class="pb-2 text-description text-sm">กำหนดชื่อที่จะแสดงด้านบนของ Modal</span>
      <h3 class="text-lg">class</h3>
      <span class="pb-2 text-description text-sm">
        className ที่สามารถ custom จัดการ root panel modal
      </span>
      <h3 class="text-lg">titleClass</h3>
      <span class="pb-2 text-description text-sm">className ที่สามารถ custom จัดการส่วน title</span>
      <h3 class="text-lg">bodyClass</h3>
      <span class="pb-2 text-description text-sm">className ที่สามารถ custom จัดการส่วน body</span>
      <h3 class="text-lg">noBackdropClose (default = false)</h3>
      <span class="pb-2 text-description text-sm">
        set = true ถ้าต้องการให้ไม่สามารถกดพื้นหลังเพื่อปิดหน้าต่าง
      </span>
      <h3 class="text-lg">noCloseIcon (default = false)</h3>
      <span class="pb-2 text-description text-sm">
        set = true ถ้าต้องการให้ไม่แสดงปุ่มกากบาทที่มุมบนขวามือ
      </span>

      <h1 class="display-heading mt-8 mb-2">Class for modal</h1>
      <h3 class="text-lg">modal-sm / modal-md / modal-lg</h3>
      <span class="pb-2 text-description text-sm">กำหนดขนาดความกว้าง Modal (default = full)</span>
      <h3 class="text-lg">modal-primary-header</h3>
      <span class="pb-2 text-description text-sm">
        แสดง Modal สไตล์ที่เน้นหัวเรื่อง (ส่วน Title พื้นหลังสี Primary)
      </span>
    </div>
  </NuxtLayout>
</template>
<script lang="tsx" setup>
import { LAYOUTS } from '~/constants/layouts'
import { ref } from '#imports'
import { useApp } from '~/hooks/useApp'
import { menuToSidebarItems, styleguideMenu } from '~/constants/routes'

const app = useApp()

app.updateDocMeta({ title: styleguideMenu.modal.name })
app.updatePageMeta({
  title: styleguideMenu.modal.name,
  breadcrumbs: [styleguideMenu.modal],
})

app.updateSidebar(menuToSidebarItems(styleguideMenu))

const customClass = ref('')
const showModal = ref(false)
const showModalLong = ref(false)

const onOpenDialog = (className?: string) => {
  showModal.value = true

  if (className) {
    customClass.value = className
  } else {
    customClass.value = ''
  }
}
</script>
