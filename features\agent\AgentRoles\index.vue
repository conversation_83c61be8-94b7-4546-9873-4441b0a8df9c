<template>
  <div>
    <Modal v-model="isShowCreateModal" title="สร้าง Role" class="modal-lg">
      <RoleForm :status="role.addStatus.value" @submit="role.add" />
    </Modal>
    <div class="flex items-center justify-between">
      <PageHeader title="ตั้งค่า Role" />
      <Button class="btn-primary btn-min" @click="isShowCreateModal = true"> สร้าง </Button>
    </div>
    <Loader :is-loading="role.fetchStatus.value.isLoading">
      <div class="mb-8 mt-4">
        <div class="sm:hidden">
          <label for="tabs" class="sr-only">เลือก Role</label>
          <select
            id="tabs"
            name="tabs"
            class="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
          >
            <option v-for="item in role.fetchItems.value" :key="item.id">{{ item.name }}</option>
          </select>
        </div>
        <div class="hidden sm:block">
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
              <p
                v-for="item in role.fetchItems.value"
                :key="item.id"
                :class="[
                  'group inline-flex items-center border-b-2 py-4 px-1 font-bold select-none',
                  {
                    'border-primary text-primary-600': activeRole?.id === item.id,
                  },
                  {
                    'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700':
                      activeRole?.id !== item.id,
                  },
                ]"
                @click="activeRole = item"
              >
                {{ item.name }}
              </p>
            </nav>
          </div>
        </div>
      </div>
      <div v-if="activeRole">
        <div class="mb-6 flex justify-end">
          <Button
            class="btn-danger btn-min"
            :is-loading="role.deleteStatus.value.isLoading"
            @click="onDelete"
          >
            ลบ
          </Button>
        </div>
        <RoleForm
          :status="role.updateStatus.value"
          :initial-values="activeRole"
          @submit="onUpdate"
        />
      </div>
    </Loader>
  </div>
</template>
<script lang="ts" setup>
import RoleForm from './RoleForm.vue'
import { useAgentPositionPageLoader } from '~/loaders/agentRole'
import { IPositionItem } from '~/models/position'

const role = useAgentPositionPageLoader()
const dialog = useDialog()
const isShowCreateModal = ref(false)
const activeRole = ref<IPositionItem | undefined>(undefined)

role.setFetchLoading()
onMounted(() => {
  role.fetch()
})

const onUpdate = (values: IPositionItem) => {
  role.update(activeRole.value!.id, values)
}

const onDelete = () => {
  dialog
    .warning({
      title: 'ลบ Role',
      message: 'คุณต้องการลบ Role นี้ใช่หรือไม่',
      isShowCancelBtn: true,
    })
    .then(() => {
      role.remove(activeRole.value!.id)
    })
}

useWatchTrue(
  () => role.fetchStatus.value.isSuccess,
  () => {
    activeRole.value = undefined

    if (role.fetchItems.value.length) {
      activeRole.value = role.fetchItems.value[0]
    } else {
      isShowCreateModal.value = true
    }
  }
)

useWatchTrue(
  () => role.addStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สร้างสำเร็จ',
        message: 'สร้างสำเร็จสำเร็จ',
      })
      .then(() => {
        isShowCreateModal.value = false
        role.fetch()
      })
  }
)

useWatchTrue(
  () => role.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'สร้างไม่สำเร็จ',
      message: role.addStatus.value.errorData,
    })
  }
)

useWatchTrue(
  () => role.updateStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'อัพเดทสำเร็จ',
        message: 'อัพเดทสำเร็จสำเร็จ',
      })
      .then(() => {
        role.fetch()
      })
  }
)

useWatchTrue(
  () => role.updateStatus.value.isError,
  () => {
    dialog.error({
      title: 'อัพเดทไม่สำเร็จ',
      message: role.updateStatus.value.errorData,
    })
  }
)

useWatchTrue(
  () => role.deleteStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'ลบสำเร็จ',
        message: 'ลบสำเร็จสำเร็จ',
      })
      .then(() => {
        role.fetch()
      })
  }
)

useWatchTrue(
  () => role.deleteStatus.value.isError,
  () => {
    dialog.error({
      title: 'ลบไม่สำเร็จ',
      message: role.deleteStatus.value.errorData,
    })
  }
)
</script>
