<template>
  <div>
    <Modal v-model="isShowCreateUserModal" title="สร้างผู้ใช้งาน" class="modal-md">
      <UsersCreateForm agent="" :status="user.addStatus.value" @submit="user.add" />
    </Modal>
    <div class="flex items-center justify-between">
      <PageHeader title="ผู้ใช้งาน" />
      <Button class="btn-primary btn-min" @click="isShowCreateUserModal = true">สร้าง</Button>
    </div>
    <Table :options="userTableOptions" @pageChange="user.fetch" @search="user.search" />
  </div>
</template>
<script lang="ts" setup>
import { useTable } from '~/hooks/useTable'
import { IUserItem } from '~/models/user'
import { COLUMN_TYPES } from '~/components/Table/types'
import UsersCreateForm from '~/features/AgentSingle/UsersCreateForm.vue'
import UsersColumnAction from '~/features/AgentSingle/UsersColumnAction.vue'

const route = useRoute()
const isShowCreateUserModal = ref(false)
const user = useAgentUserPageLoader(route.params.id as string)
const dialog = useDialog()

user.setFetchLoading()
onMounted(() => {
  user.fetch()
})

const userTableOptions = useTable<IUserItem>({
  repo: user,
  columns: () => [
    {
      value: 'username',
    },
    {
      value: 'ชื่อ',
    },
    {
      value: 'ตำแหน่ง',
    },
    {
      value: 'IP',
    },
    {
      value: 'สถานะ',
    },
    {
      value: 'สร้างเมื่อ',
    },
    {
      value: '',
    },
  ],
  rows: (items) =>
    items.map((item) => {
      return [
        {
          value: item.username,
        },
        {
          value: item.full_name,
        },
        {
          value: item.role,
        },
        {
          value: item.last_ip,
        },
        {
          value: item.is_enabled,
          type: COLUMN_TYPES.BOOLEAN,
        },
        {
          value: item.created_at,
          type: COLUMN_TYPES.DATE_TIME,
        },
        {
          value: UsersColumnAction,
          type: COLUMN_TYPES.COMPONENT,
          props: {
            user: item,
          },
          on: {
            reload: () => {
              user.fetch()
            },
          },
        },
      ]
    }),
})

useWatchTrue(
  () => user.addStatus.value.isSuccess,
  () => {
    dialog
      .success({
        title: 'สำเร็จ',
        message: 'สร้างผู้ใช้งานสำเร็จ',
      })
      .then(() => {
        isShowCreateUserModal.value = false
        user.fetch()
      })
  }
)

useWatchTrue(
  () => user.addStatus.value.isError,
  () => {
    dialog.error({
      title: 'ผิดพลาด',
      message: StringHelper.getError(user.addStatus.value.errorData),
    })
  }
)
</script>
