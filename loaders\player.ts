import { defineStore } from 'pinia'
import { usePageLoader } from '~/lib/api/loaderPage'
import { type IPlayerItem } from '~/models/player'
import { useObjectLoader } from '~/lib/api/loaderObject'
import { type IBankItem } from '~/models/bank'
import { useRequestOptions } from '~/hooks/useRequestOptions'

export const useAgentPlayerPageLoader = defineStore('agent.player.page', () => {
  const { getDefaultWithAuth } = useRequestOptions()

  return usePageLoader<IPlayerItem>({
    baseURL: '/agent/players',
    getBaseRequestOptions: () => getDefaultWithAuth(),
  })
})

export const useAgentPlayerChangePasswordLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<IPlayerItem, { new_password: string }>({
    url: `/agent/players/${userId}/change-password`,
    method: 'POST',
    getRequestOptions: getDefaultWithAuth,
  })
}

export const useAgentPlayerUpdateBankLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<
    IBankItem,
    {
      bank_id: string
      bank_number: string
      first_name: string
      last_name: string
    }
  >({
    url: `/agent/players/${userId}/change-bank`,
    method: 'POST',
    getRequestOptions: getDefaultWithAuth,
  })
}

export const useAgentPlayerCreditPlusLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<
    any,
    {
      credit: number
    }
  >({
    url: `/agent/players/${userId}/add-credit`,
    method: 'POST',
    getRequestOptions: getDefaultWithAuth,
  })
}

export const useAgentPlayerCreditMinusLoader = (userId: string) => {
  const { getDefaultWithAuth } = useRequestOptions()

  return useObjectLoader<
    any,
    {
      credit: number
    }
  >({
    url: `/agent/players/${userId}/remove-credit`,
    method: 'POST',
    getRequestOptions: getDefaultWithAuth,
  })
}
