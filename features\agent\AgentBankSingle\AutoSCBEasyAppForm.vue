<template>
  <div>
    <Form v-if="!requestStatus.isSuccess" @submit="onSubmitRequest">
      <FormFields :options="formFields1" :form="form1" />
      <Button
        type="submit"
        class="btn-primary btn-min w-full"
        :is-loading="requestStatus.isLoading"
      >
        ส่ง OTP
      </Button>
    </Form>
    <Form v-else @submit="onSubmit">
      <FormFields :options="formFields2" :form="form2" />
      <Button type="submit" class="btn-primary btn-min w-full" :is-loading="submitStatus.isLoading">
        ยืนยัน OTP
      </Button>
    </Form>
  </div>
</template>
<script lang="ts" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { IStatus } from '~/lib/api/types'

const emits = defineEmits(['otp-submit', 'submit'])

defineProps<{
  requestStatus: IStatus
  submitStatus: IStatus
}>()

const form1 = useForm()
const form2 = useForm()

const formFields1 = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เลขบัตรประจำตัวประชาชน',
      name: 'card_id',
      rules: yup.string().numeric().len(13).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'วันเดือนปีเกิด (1995-10-01)',
      name: 'date_of_birth',
      placeholder: '1995-10-01',
      rules: yup.string().length(10).required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เบอร์โทรศัพท์',
      name: 'mobile_number',
      placeholder: '08xxxxxxxx',
      rules: yup.string().numeric().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'พิน 6 หลัก',
      name: 'pin',
      rules: yup.string().numeric().len(6).required(),
    },
  },
])

const formFields2 = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'OTP',
      name: 'otp',
      rules: yup.string().required(),
    },
  },
])

const onSubmitRequest = form1.handleSubmit((data) => {
  emits('otp-submit', data)
})

const onSubmit = form2.handleSubmit((data) => {
  emits('submit', data)
})
</script>
