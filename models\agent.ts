export interface IAgentWebConfigItem {
  primary_color: string
  secondary_color: string
  ternary_color: string
  logo_url: string
  line: string
  title: string
  description: string
  keywords: string
  favicon_url: string
  cover_url: string
}

export interface IAgentNoticeItem {
  id: string
  agent_id: string
  type: string
  tag: string
  image_urls: string[]
  created_at: string
}

export interface IAgentItem {
  id: string
  created_at: string
  updated_at: string
  prefix: string
  name: string
  domain: string
  credit: number
  remark?: string
  is_enabled: boolean
  bank_accounts: any
  website_config: IAgentWebConfigItem
}
