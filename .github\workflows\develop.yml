name: Digitalocean develop CICD

on:
  # push:
  #   branches:
  #   - develop
  workflow_dispatch: {}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checking out this repository
        uses: actions/checkout@v2

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ***********************************************************************

      - name: Logging in to DigitalOcean
        run: doctl registry login

      - name: build container
        run: |
          docker build \
          --build-arg HOST=0.0.0.0 \
          --build-arg PORT=3000 \
          --build-arg APP_BASE_API=https://api.wiwp.dev \
          --build-arg APP_BASE_API_MOCK=https://admin.wiwp.dev \
          -t registry.digitalocean.com/wink-dev/admin-frontend-dev:${{ github.sha }}  .
          docker tag registry.digitalocean.com/wink-dev/admin-frontend-dev:${{ github.sha }} registry.digitalocean.com/wink-dev/admin-frontend-dev:latest
          docker push registry.digitalocean.com/wink-dev/admin-frontend-dev:${{ github.sha }}
          docker push registry.digitalocean.com/wink-dev/admin-frontend-dev:latest

  deploy:
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Checkout Kustomize
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.PAT }}
          repository: winkplus/winkplus-k8s
          ref: main
      - name: Set Image
        uses: stefanprodan/kube-tools@v1
        with:
          kubectl: 1.24.0
          kustomize: 4.5.4
          command: |
            cd overlays/develop
            kustomize edit set image registry.digitalocean.com/wink-dev/admin-frontend-dev=registry.digitalocean.com/wink-dev/admin-frontend-dev:${{ github.sha }}
            git config --global user.email "<EMAIL>"
            git config --global user.name "CI bot"
            git add .
            git commit -m "build: update admin-frontend-dev image tag to ${{ github.sha }}"
            git push
