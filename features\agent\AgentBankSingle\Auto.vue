<template>
  <div class="p-6">
    <AutoSCBEasyApp v-if="item.bank.slug === 'SCB'" :item="item" />
  </div>
</template>
<script lang="tsx" setup>
import { PropType } from 'vue/dist/vue'
import { IBankItem } from '~/models/bank'
import AutoSCBEasyApp from '~/features/agent/AgentBankSingle/AutoSCBEasyApp.vue'

defineProps({
  item: {
    type: Object as PropType<IBankItem>,
    required: true,
  },
})
</script>
