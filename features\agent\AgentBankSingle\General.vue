<template>
  <Form class="p-6" @submit="onSubmit">
    <FormFields :options="formFields" />
    <div class="flex justify-end space-x-3">
      <Button class="btn-secondary btn-min" @click="router.back()"> ยกเลิก </Button>
      <Button type="submit" class="btn-primary btn-min"> อัพเดต </Button>
    </div>
  </Form>
</template>
<script lang="tsx" setup>
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { PropType } from 'vue'
import { createFormFields } from '~/hooks/useForm'
import { INPUT_TYPES } from '~/components/Form/types'
import { useAgentBankPageLoader, useBankListLoader, useDialog, useRoute } from '#imports'
import { IBankItem } from '~/models/bank'

const props = defineProps({
  item: {
    type: Object as PropType<IBankItem>,
    required: true,
  },
})

const dialog = useDialog()
const route = useRoute()
const router = useRouter()
const bankPage = useBankListLoader()
const bank = useAgentBankPageLoader()

const form = useForm<
  Partial<{
    bank_id: string
    account_number: string
    account_name: string
    allowed_bank: string[]
    type: string
    min_balance: number
    fee: number
    enabled_withdraw_otp: boolean
    enabled_show_on_web: boolean
  }>
>()

bankPage.setLoading()
onMounted(async () => {
  await bankPage.run()

  form.setValues(props.item)
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'ธนาคาร',
      name: 'bank_id',
      rules: yup.string().required(),
      options: ArrayHelper.toOptions(bankPage.items.value, 'id', 'name_th'),
      isLoading: bankPage.status.value.isLoading,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'เลขบัญชี',
      name: 'account_number',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'ชื่อบัญชี',
      name: 'account_name',
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.MULTI_SELECT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'ธนาคารที่อนุญาติให้เห็น',
      name: 'allowed_bank',
      rules: yup.array().min(1).required(),
      options: ArrayHelper.toOptions(bankPage.items.value, 'slug', 'name_th'),
      isLoading: bankPage.status.value.isLoading,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'ประเภท',
      name: 'type',
      options: [
        {
          label: 'บัญชีฝากและถอน',
          value: 'deposit_and_withdraw',
        },
        {
          label: 'บัญชีฝาก',
          value: 'deposit',
        },
        {
          label: 'บัญชีถอน',
          value: 'withdraw',
        },
        {
          label: 'บัญชีพักเงิน',
          value: 'idle',
        },
      ],
      rules: yup.string().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'สำหรับลูกค้าที่มียอดฝาก',
      name: 'min_balance',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'ค่าธรรมเนียม',
      name: 'fee',
      rules: yup.number().required(),
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    isHide: true,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'เปิดใช้งาน OTP ถอน',
      name: 'enabled_withdraw_otp',
    },
  },
  {
    type: INPUT_TYPES.TOGGLE_SWITCH,
    props: {
      classInner: '!flex-row items-center space-x-4',
      classLabel: 'w-[150px]',
      label: 'เปิดใช้งาน (แสดงหน้าเว็บ)',
      name: 'enabled_show_on_web',
    },
  },
])

const onSubmit = form.handleSubmit((data) => {
  bank.update(route.params.id as string, {
    ...data,
    allowed_bank: ArrayHelper.toArray(data.allowed_bank).map((item) => item.value),
    min_balance: Number(data.min_balance),
    fee: Number(data.fee),
  })
})

useWatchTrue(
  () => bank.updateStatus.isSuccess,
  () => {
    dialog.success({
      title: 'สำเร็จ',
      message: 'แก้ไขแบงค์สำเร็จ',
    })
  }
)

useWatchTrue(
  () => bank.updateStatus.isError,
  () => {
    dialog.error({
      title: 'เกิดข้อผิดพลาด',
      message: StringHelper.getError(bank.updateStatus.errorData),
    })
  }
)
</script>
